package cn.ijiami.detection.service.impl;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.message.MessageNotificationSendKit;
import cn.ijiami.detection.message.param.AnalysisProgressParam;
import cn.ijiami.detection.message.param.UploadMessageParam;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.bean.ChunkMergeSort;
import cn.ijiami.detection.common.datamodels.EncryptCompanyDataModel;
import cn.ijiami.detection.constant.DistributedLockConstant;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.exception.ChunkFileMergeException;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.AddApp;
import cn.ijiami.detection.query.BaseQuery;
import cn.ijiami.detection.query.BatchAddApp;
import cn.ijiami.detection.result.AppBasicInfo;
import cn.ijiami.detection.result.AppDetailsResult;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.thread.FixedThreadPoolManager;
import cn.ijiami.detection.thread.IOSResignThread;
import cn.ijiami.detection.utils.*;
import cn.ijiami.detection.utils.hap.HapInfo;
import cn.ijiami.detection.utils.sign.ApkV2SignInfo;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.apk.entity.ApkInfo;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.file.service.impl.DefaultFileService;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ijiami.ios.ipatools.IpaInfo;
import com.ijmsd.analyzeapktool.UnpackAAB;
import com.ijmsd.analyzeapktool.UnpackAPK;
import com.ijmsd.common.exception.DecompileException;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.ijiami.detection.constant.PinfoConstant.CACHE_ASSETS_ANALYZE;
import static cn.ijiami.detection.utils.CommonUtil.*;


/**
 * 资产接口实现类
 *
 * <AUTHOR>
 */
@Service
public class AssetsServiceImpl implements IAssetsService {

    private static final Logger LOG = LoggerFactory.getLogger(AssetsServiceImpl.class);

    private static final String STOP_FLAG = "1";

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;DetectionTaskControllerDetectionTaskController

    @Autowired
    private TApkCategoryMapper apkCategoryMapper;

    @Autowired
    private IBaseFileService fileService;

    @Autowired
    private DefaultFileService defaultService;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    @Autowired
    private IStorageLogService storageLogService;

    @Autowired
    private TChunkUploadFileMapper chunkUploadFileMapper;

    @Autowired
    private TChunkUploadPartMapper chunkUploadPartMapper;

    @Autowired
    private IAssetsOfBigDataService assetsOfBigDataService;

    @Autowired
    private MessageNotificationSendKit messageNotificationSendKit;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Value("${fastDFS.intranet.ip}")
    private String fastDFSIntranetIp;

    @Value("${ijiami.tools.path}")
    private String toolsPath;

    /**
     * apk包、ipa包等临时文件上传目录
     */
    @Value("${ijiami.framework.extract.path:/opt/detection/file/}")
    private String fileExtractPath;

    @Autowired
    @Qualifier("redisDistributedLock")
    private DistributedLockService distributedLockService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;
    
    @Autowired
    @Lazy
    private ITaskService taskService;

    @Autowired
    @Lazy
    private IPrivacyDetectionService privacyDetectionService;

    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    private static final Set<String> PRIVACY_POLICY_FILE_TYPES = new HashSet<>();

    //第三方清单可上传类型
    private static final Set<String> THIRD_PARTY_LIST_FILE_TYPES = new HashSet<>();

    private static final Set<String> OBB_FILE_TYPES = new HashSet<>();

    static {
        // 初始化txt文件判断
        PRIVACY_POLICY_FILE_TYPES.add("txt");
        THIRD_PARTY_LIST_FILE_TYPES.add("xlsx");
        OBB_FILE_TYPES.add("obb");
    }

    @Override
    public int insert(TAssets assets) {
        return assetsMapper.insert(assets);
    }

    @Override
    public AssetsVO findAssetsListPage(TAssets assets) {
        AssetsVO assetsVO = new AssetsVO();
        if (assets.getPage() != null && assets.getRows() != null) {
            PageHelper.startPage(assets.getPage(), assets.getRows());
        }
        List<AssetsListVO> assetsList = assetsMapper.findAssetsList(assets);
        // 文件地址修改
        assetsList.forEach(assetsListVO -> {
            if (StringUtils.isNotBlank(assetsListVO.getAppUrl(fastDFSIp))) {
                assetsListVO.setShellIpaPath(assetsListVO.getAppUrl(fastDFSIp));
            } else {
                assetsListVO.setShellIpaPath(StringUtils.EMPTY);
            }
            if(StringUtils.isNotBlank(assetsListVO.getLogo()) && !assetsListVO.getLogo().contains("http") && assetsListVO.getLogo().contains("group")) {
            	assetsListVO.setLogo(fastDFSIp + "/" + assetsListVO.getLogo());
            }
            if (StringUtils.isNotBlank(assetsListVO.getPrivacyPolicyPath())) {
                assetsListVO.setPrivacyPolicyPath(fastDFSIp + "/" + assetsListVO.getPrivacyPolicyPath());
            } else {
                assetsListVO.setPrivacyPolicyPath(StringUtils.EMPTY);
            }
            if (StringUtils.isNotBlank(assetsListVO.getThirdPartyShareListPath())){
                assetsListVO.setThirdPartyShareListPath(fastDFSIp + "/" + assetsListVO.getThirdPartyShareListPath());
            }else{
                assetsListVO.setThirdPartyShareListPath(StringUtils.EMPTY);
            }
            if (StringUtils.isNotBlank(assetsListVO.getAssetsFunctionType())) {
                List<String> apkCategoryNames = apkCategoryMapper.apkCategoryNames(Arrays.stream(assetsListVO.getAssetsFunctionType().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                assetsListVO.setCategoryNameList(apkCategoryNames);
            }
            if (StringUtils.isNotBlank(assetsListVO.getObbDataPath())) {
                assetsListVO.setObbDataPath(fastDFSIp + "/" + assetsListVO.getObbDataPath());
            }
            if (StringUtils.isNotEmpty(assetsListVO.getQrcodePath())) {
                assetsListVO.setQrcodePath(fastDFSIp + "/" + assetsListVO.getQrcodePath());
            }
        });
        List<TAssetsCount> countList = assetsMapper.assetsTerminalTypeCount(assets);
        assetsVO.setAllAppSize((int) countList.stream().filter(Objects::nonNull).map(TAssetsCount::getAllSize).filter(Objects::nonNull).count());
        assetsVO.setAndroidAppSize(countList.stream().filter(Objects::nonNull).mapToInt(TAssetsCount::getAndroidSize).filter(Objects::nonNull).sum());
        assetsVO.setIosAppSize(countList.stream().filter(Objects::nonNull).mapToInt(TAssetsCount::getIosSize).filter(Objects::nonNull).sum());
        assetsVO.setWechatAppletSize(countList.stream().filter(Objects::nonNull).mapToInt(TAssetsCount::getWechatAppletSize).filter(Objects::nonNull).sum());
        assetsVO.setAlipayAppletSize(countList.stream().filter(Objects::nonNull).mapToInt(TAssetsCount::getAlipayAppletSize).filter(Objects::nonNull).sum());
        assetsVO.setHarmonySize(countList.stream().filter(Objects::nonNull).mapToInt(TAssetsCount::getHarmonySize).filter(Objects::nonNull).sum());
        assetsVO.setPageInfoAssets(new PageInfo<>(assetsList));
        return assetsVO;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public TAssets analysisApk(FileVO fileVO, boolean bool, Long userId, boolean isAbsolutePath) throws IjiamiApplicationException {
    	return analysisApk(fileVO,  bool,  userId,  isAbsolutePath,null);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public TAssets analysisApk(FileVO fileVO, boolean bool, Long userId, boolean isAbsolutePath, String shellPath) throws IjiamiApplicationException {
        return analysisApkInternal(fileVO, userId, isAbsolutePath, shellPath, genFileMD5(fileVO));
    }

    private String genFileMD5(FileVO fileVO) {
        String filePath = fileVO.getFilePath();
        String fileMD5  = analyzeMD5ByFileName(fileVO.getFileName());
        if (StringUtils.isEmpty(fileMD5)) {
            fileMD5 =  MD5Util.getFileMD5(filePath);
        }
        return fileMD5;
    }

    private TAssets analysisApkInternal(FileVO fileVO, Long userId, boolean isAbsolutePath, String shellPath, String validTag) throws IjiamiApplicationException {
        // 发送信息，提醒开始解析
        validAnalysisStatus(validTag, userId);
        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 15, validTag);
        // 校验资产是否重复
        TAssets assets = analysis(fileVO, userId, isAbsolutePath, shellPath, validTag);
        LOG.info("analysis success");
        if (StringUtils.isNotBlank(shellPath)) {
            assets.setShellIpaPath(shellPath);
        }
        // 保存log
        saveApkLogo(new FileVO(), assets);
        LOG.info("saveApkLogo success");
        // 添加资产存储记录
        saveStorageLog(assets, userId);
        LOG.info("saveStorageLog success");
        //删除本地apk
        File file = new File(fileVO.getFilePath());
        if (file.exists()) {
            LOG.info("exists apk path={}", file.getAbsolutePath());
            LOG.info("delete disk apk status={}", file.delete());
        }
        return assets;
    }

    @Async("commonExecutor")
    void saveStorageLog(TAssets assets, Long userId){
    	storageLogService.saveStorageLogByAsset(assets, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveApkLogo(FileVO fileVO, TAssets assets) throws IjiamiApplicationException {
        // 获取图片加密的key值
        String fileKey = assets.getLogo();
        String relativePath = fileKey;
        if(!StringUtils.isEmpty(fileKey) && !fileKey.contains("group")) {
        	relativePath = FileUtil.decodeData(fileKey);
        }
        String fileExtName = relativePath.substring(relativePath.lastIndexOf(".") + 1).toUpperCase();
        String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
        // ios图片存在转换问题，在保存logo之前做一次转换，防止转pdf图片无法显示
        if (assets.getTerminalType() == TerminalTypeEnum.IOS) {
            // 获取图片的绝对路径
            String absolutePath = commonProperties.getFilePath() + relativePath;
            HtmlToPdf.convertImg(absolutePath);
        }
        // 保存图片信息
        fileVO.setFileKey(fileKey);
        fileVO.setRelativePath(relativePath);
        fileVO.setFileExtName(fileExtName);
        fileVO.setFileName(fileName);
        fileVO.setFileSize(0L);
        fileService.saveFile(fileVO);
    }

    @Override
    public TAssets findTassetsByUserAndMd5(Long userId, String md5) {
        return assetsMapper.findOneByUserAndMd5(userId, md5);
    }

    /**
     * 获取资产包地址
     * @param assetsId
     * @return
     * @throws IjiamiApplicationException
     */
    public Map<String,String> getFileByAssetsId(Long assetsId) throws IjiamiApplicationException{
        Map<String,String> map = new HashMap<>();
        if(assetsId==null) {
            throw new IjiamiApplicationException("资产ID不能为空！");
        }
        TAssets assets = new TAssets();
        assets.setId(assetsId);
        assets.setIsDelete(0);
        List<TAssets> list = assetsMapper.select(assets);
        if(list==null || list.size()==0){
            throw new IjiamiApplicationException("资产不存在！");
        }
        String url=list.get(0).getShellIpaPath();
        if(StringUtils.isEmpty(url)){
            throw new IjiamiApplicationException("资产下载路径不存在");
        }
        String fileName=StringUtils.isEmpty(list.get(0).getSourceFileName())?url.substring(url.lastIndexOf("/")):list.get(0).getSourceFileName();
        if (StringUtils.startsWith(url, "http")) {
            map.put("url", url);
        } else {
            map.put("url",fastDFSIp+"/"+url);
        }
        map.put("fileName",fileName);
        return map;
    }

    /**
     * 获取资产包地址
     * @param assetsId
     * @return
     * @throws IjiamiApplicationException
     */
    public String getDumpByAssetsId(Long assetsId) throws IjiamiApplicationException {
        if(assetsId==null) {
            throw new IjiamiApplicationException("资产ID不能为空！");
        }
        TAssets assets = new TAssets();
        assets.setId(assetsId);
        assets.setIsDelete(0);
        List<TAssets> list = assetsMapper.select(assets);
        if(list == null || list.size()==0){
            throw new IjiamiApplicationException("资产不存在！");
        }
        String url = list.get(0).getFastDFSDumpZipUrl(fastDFSIp);
        if(StringUtils.isEmpty(url)){
            throw new IjiamiApplicationException("资产下载路径不存在");
        }
        return url;
    }


    @Override
    public UploadAssetsAppendixResponse appPrivacyPolicyUpload(MultipartFile policyPolicyFile, String appFileMd5, Long userId) throws Exception {
        // 不为空时，需要验证app文件是否已经上传
        if (StringUtils.isNotBlank(appFileMd5)) {
            TStorageLog appFileLog = storageLogService.findOneByAssociationKey(
                    appFileMd5,
                    Arrays.asList(StorageFileType.ANDROID, StorageFileType.IOS, StorageFileType.HARMONY),
                    userId);
            if (Objects.isNull(appFileLog)) {
                LOG.info("上传失败，找不到关联的APP appFileMd5={}", appFileMd5);
                throw new IjiamiRuntimeException("上传失败，找不到关联的APP。请重新上传APP");
            }
        } else {
            appFileMd5 = UuidUtil.uuid();
        }
        FileVO fileVO = saveAppPrivacyPolicy(policyPolicyFile);
        storageLogService.saveStorageLogByFastDfsPath(appFileMd5, fileVO.getFilePath(), StorageFileType.TXT, userId);
        return new UploadAssetsAppendixResponse(appFileMd5);
    }

    @Override
    public UploadAssetsAppendixResponse thirdPartySharedListUpload(MultipartFile thirdPartyFile, String appFileMd5, Long userId) throws Exception {
        // 不为空时，需要验证app文件是否已经上传
        if (StringUtils.isNotBlank(appFileMd5)) {
            TStorageLog appFileLog = storageLogService.findOneByAssociationKey(appFileMd5,
                    Arrays.asList(StorageFileType.ANDROID, StorageFileType.IOS, StorageFileType.HARMONY), userId);
            if (Objects.isNull(appFileLog)) {
                LOG.info("上传失败，找不到关联的APP appFileMd5={}", appFileMd5);
                throw new IjiamiRuntimeException("上传失败，找不到关联的APP。请重新上传APP");
            }
        } else {
            appFileMd5 = UuidUtil.uuid();
        }
        FileVO fileVO = saveThirdPartySharedList(thirdPartyFile);
        storageLogService.saveStorageLogByFastDfsPath(appFileMd5, fileVO.getFilePath(), StorageFileType.XLSX, userId);
        return new UploadAssetsAppendixResponse(appFileMd5);
    }

    @Override
    public UpdateOrAddAssetsResponse updateOrAddAppPrivacyPolicy(MultipartFile policyPolicyFile, Long assetsId, Long userId) throws Exception {
        TAssets assets = assetsMapper.selectByPrimaryKey(assetsId);
        if (Objects.isNull(assets)) {
            throw new IjiamiRuntimeException("assetsId错误，资产不存在");
        }
        FileVO fileVO = saveAppPrivacyPolicy(policyPolicyFile);
        TAssets updateAssets = new TAssets();
        updateAssets.setPrivacyPolicyPath(fileVO.getFilePath());
        Example example = new Example(TAssets.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", assetsId);
        assetsMapper.updateByExampleSelective(updateAssets, example);
        return new UpdateOrAddAssetsResponse(fastDFSIp + "/" + fileVO.getFilePath());
    }

    private FileVO saveAppPrivacyPolicy(MultipartFile policyPolicyFile) throws Exception {
        String extName = FilenameUtils.getExtension(policyPolicyFile.getOriginalFilename());
        File diskFile = new File(commonProperties.getProperty("ijiami.framework.file.path"), UuidUtil.uuid() + "." + extName);
        multipartFileSaveToDisk(policyPolicyFile, diskFile);
        String fileType = FileTypeUtil.getType(diskFile);
        // 文件类型是允许上传的
        if (Objects.nonNull(fileType) && PRIVACY_POLICY_FILE_TYPES.contains(fileType.toLowerCase())) {
            // 上传的文件扩展名与文件实际内容不一致，修改为正确的扩展名
            if (!fileType.equals(extName)) {
                File newName = new File(diskFile.getAbsolutePath().replace("." + extName, "." + fileType));
                if (diskFile.renameTo(newName)) {
                    LOG.info("修改名字成功 old={} new={}", diskFile.getName(), newName.getName());
                    diskFile = newName;
                }
            }
            FileVO uploadDfsFile = FileVOUtils.convertFileVOByFile(diskFile);
            FileVO fileVO = singleFastDfsFileService.instance().storeFile(uploadDfsFile);
            LOG.info("上传APP隐私协议到FastDFs filePath={}", fileVO.getFilePath());
            return fileVO;
        } else {
            LOG.info("文件类型错误 fileType={}", fileType);
            throw new IjiamiRuntimeException("文件类型错误，只能上传txt文件");
        }
    }

    @Override
    public UpdateOrAddAssetsResponse updateOrAddThirdPartySharedList(MultipartFile thirdPartyFile, Long assetsId, Long userId) throws Exception {
        TAssets assets = assetsMapper.selectByPrimaryKey(assetsId);
        if (Objects.isNull(assets)) {
            throw new IjiamiRuntimeException("assetsId错误，资产不存在");
        }
        FileVO fileVO = saveThirdPartySharedList(thirdPartyFile);
        TAssets updateAssets = new TAssets();
        updateAssets.setThirdPartyShareListPath(fileVO.getFilePath());
        Example example = new Example(TAssets.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", assetsId);
        assetsMapper.updateByExampleSelective(updateAssets, example);
        return new UpdateOrAddAssetsResponse(fastDFSIp + "/" + fileVO.getFilePath());
    }

    @Override
    public String extractAppSignature(String appFilePath) {
        AppSignatureInfoUtil.AppSignatureInfo signatureInfo;
        if (StringUtils.endsWithIgnoreCase(appFilePath, ConstantsUtils.SUFFIX_END_HAP)) {
            signatureInfo = AppSignatureInfoUtil.extractHapSignatureInfo(toolsPath + File.separator + "harmony", appFilePath);
        } else {
            signatureInfo = AppSignatureInfoUtil.extractApkSignatureInfo(appFilePath);
        }
        if (signatureInfo != null) {
            return "发布者:" + signatureInfo.getIssuerDN() + " 所有者:" + signatureInfo.getSubjectDN();
        } else {
            return StringUtils.EMPTY;
        }
    }

    @Override
    public AppDetailsResult retrieveAppBasicInfo(TTask task, TAssets assets) throws IjiamiApplicationException {
        if (haveAppBasicInfo(assets)) {
            AppDetailsResult result = new AppDetailsResult();
            AppBasicInfo info = buildAppBasicInfo(assets);
            info.setSignDetail(assets.getSignature());
            info.setIjiamiEncryption(org.apache.commons.lang3.StringUtils.contains(assets.getEncryptCompany(), "爱加密"));
            info.setEncryptCompany(assets.getEncryptCompany());
            info.setTargetSdkVersion(assets.getTargetSdkVersion());
            info.setMinSdkVersion(assets.getMinSdkVersion());
            result.setBasicInfo(info);
            result.setPermissionList(getPermissionsFromAssets(assets));
            return result;
        } else {
            BaseMessageVO baseMessageVO = taskService.getBaseMessage(task.getApkDetectionDetailId());
            if (baseMessageVO != null) {
                return buildAppDetailsResultFromBaseMessage(baseMessageVO, task, assets);
            } else {
                if (assets.getTerminalType().isApplet()) {
                    AppDetailsResult result = new AppDetailsResult();
                    AppBasicInfo info = buildAppBasicInfo(assets);
                    info.setIjiamiEncryption(false);
                    info.setEncryptCompany(org.apache.commons.lang3.StringUtils.EMPTY);
                    info.setSignDetail(org.apache.commons.lang3.StringUtils.EMPTY);
                    result.setBasicInfo(info);
                    result.setPermissionList(Collections.emptyList());
                    return result;
                } else {
                    startExtractAppInfoTask(assets);
                    checkAnalyzeTaskFinish(assets, TimeUnit.MINUTES.toMillis(10));
                    TAssets newAssets = assetsMapper.selectByPrimaryKey(assets.getId());
                    if (haveAppBasicInfo(newAssets)) {
                        return buildAppDetailResult(newAssets);
                    }
                    throw new IjiamiApplicationException("获取数据失败");
                }
            }
        }
    }

    private AppDetailsResult buildAppDetailResult(TAssets assets) {
        AppDetailsResult result = new AppDetailsResult();
        AppBasicInfo info = buildAppBasicInfo(assets);
        info.setSignDetail(assets.getSignature());
        info.setIjiamiEncryption(org.apache.commons.lang3.StringUtils.contains(assets.getEncryptCompany(), "爱加密"));
        info.setEncryptCompany(assets.getEncryptCompany());
        info.setTargetSdkVersion(assets.getTargetSdkVersion());
        info.setMinSdkVersion(assets.getMinSdkVersion());
        result.setBasicInfo(info);
        result.setPermissionList(getPermissionsFromAssets(assets));
        return result;
    }

    /**
     * 从基本信息构建应用详细信息结果
     * @param baseMessageVO 基本信息对象
     * @param task 任务对象
     * @param assets 资产对象
     * @return AppDetailsResult 应用详细信息结果
     */
    private AppDetailsResult buildAppDetailsResultFromBaseMessage(BaseMessageVO baseMessageVO, TTask task, TAssets assets) {
        AppDetailsResult result = new AppDetailsResult();
        AppBasicInfo info = buildAppBasicInfo(assets);
        info.setSignDetail(baseMessageVO.getSignDetail());
        info.setIjiamiEncryption(org.apache.commons.lang3.StringUtils.contains(baseMessageVO.getEncryptDetail(), "爱加密"));
        info.setEncryptCompany(baseMessageVO.getEncryptDetail());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(baseMessageVO.getTargetSdkVersion())) {
            info.setTargetSdkVersion(Integer.parseInt(baseMessageVO.getTargetSdkVersion()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(baseMessageVO.getMinSdkVersion())) {
            info.setMinSdkVersion(Integer.parseInt(baseMessageVO.getMinSdkVersion()));
        }
        List<String> detectionPermissions = privacyDetectionService.getDetectionPermissions(task.getApkDetectionDetailId());
        // 使用静态检测的内容，并更新到数据库
        updateToDb(assets.getId(), info, detectionPermissions);
        result.setBasicInfo(info);
        result.setPermissionList(getPermissionVOList(detectionPermissions, task.getTerminalType()));
        return result;
    }

    private void checkAnalyzeTaskFinish(TAssets assets, long timeoutMillis) {
        String cacheKey = CACHE_ASSETS_ANALYZE + assets.getId();
        if (Objects.isNull(cacheService.getLong(cacheKey))) {
            return;
        }
        long sleepTime = 1000;
        while (timeoutMillis > 0) {
            if (Objects.nonNull(cacheService.getLong(cacheKey))) {
                try {
                    Thread.sleep(sleepTime);
                } catch (Exception e) {
                    LOG.error("checkAnalyzeTaskFinish error", e);
                }
            }
            timeoutMillis = timeoutMillis - sleepTime;
        }
    }

    private FileVO saveThirdPartySharedList(MultipartFile thirdPartyFile) throws Exception {
        String extName = FilenameUtils.getExtension(thirdPartyFile.getOriginalFilename());
        File diskFile = new File(commonProperties.getProperty("ijiami.framework.file.path"), UuidUtil.uuid() + "." + extName);
        multipartFileSaveToDisk(thirdPartyFile, diskFile);
        String fileType = FileTypeUtil.getType(diskFile);
        // 文件类型是允许上传的
        if (Objects.nonNull(fileType) && THIRD_PARTY_LIST_FILE_TYPES.contains(fileType.toLowerCase())) {
            // 上传的文件扩展名与文件实际内容不一致，修改为正确的扩展名
            if (!fileType.equals(extName)) {
                File newName = new File(diskFile.getAbsolutePath().replace("." + extName, "." + fileType));
                if (diskFile.renameTo(newName)) {
                    LOG.info("修改名字成功 old={} new={}", diskFile.getName(), newName.getName());
                    diskFile = newName;
                }
            }

            //得到文件流
            try(InputStream in = Files.newInputStream(diskFile.getAbsoluteFile().toPath())){
                Workbook workbook = new XSSFWorkbook(in);
                //获取第一张表
                Sheet sheet = workbook.getSheetAt(0);
                //得到第二行
                Row row = sheet.getRow(1);
                if(null == row){
                    LOG.info("上传的excle文件为空");
                    throw new IjiamiRuntimeException("检测到上传的excle文件内容与模板内容不匹配，请重新上传");
                }
                //*SDK名称
                String cell1 = row.getCell(0).getStringCellValue();
                //包名
                String cell2 = row.getCell(1).getStringCellValue();
                //具体功能
                String cell3 = row.getCell(2).getStringCellValue();
                //公司名称
                String cell4 = row.getCell(3).getStringCellValue();
                //客户端
                String cell5 = row.getCell(4).getStringCellValue();
                //*获取信息/权限行为
                String cell6 = row.getCell(5).getStringCellValue();
                //隐私权政策
                String cell7 = row.getCell(6).getStringCellValue();
                if(!"*SDK名称".equals(cell1) || !"包名".equals(cell2) || !"具体功能".equals(cell3) || !"公司名称".equals(cell4)
                        || !"客户端".equals(cell5) || !"*获取信息/权限行为".equals(cell6) || !"隐私权政策".equals(cell7)){
                    LOG.info("");
                    throw new IjiamiRuntimeException("上传的excle文件与模板文件内容不匹配，请重新上传");
                }
                workbook.close();
            }catch (IllegalStateException e){
                throw new IjiamiRuntimeException("上传的excle文件与模板文件内容不匹配，请重新上传");
            }

            FileVO uploadDfsFile = FileVOUtils.convertFileVOByFile(diskFile);
            FileVO fileVO = singleFastDfsFileService.instance().storeFile(uploadDfsFile);
            LOG.info("上传APP第三方共享清单到FastDFs filePath={}", fileVO.getFilePath());
            return fileVO;
        } else {
            LOG.info("文件类型错误 fileType={}", fileType);
            throw new IjiamiRuntimeException("文件错误，只能上传模板文件");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse<TAssets> analysisApk(Long userId, FileVO fileVO, boolean isAbsolutePath) throws IjiamiApplicationException {
        BaseResponse<TAssets> baseResponse = new BaseResponse<>();
        // 校验资产是否重复
        TAssets assets = checkAssets(userId, fileVO);
        if (assets == null) {
            assets = analysis(fileVO, userId, isAbsolutePath,null, genFileMD5(fileVO));
        } else {
            String filePath = fileVO.getFilePath();
            File file = new File(filePath);
            file.delete();
            baseResponse.setStatus(10001);
            baseResponse.setMessage("上传文件失败，该应用已上传");
        }
        baseResponse.setData(assets);
        return baseResponse;
    }

    void sendUploadMessage(Long userId,Integer progress,String fileName){
        messageNotificationSendKit.sendUploadMessage(new UploadMessageParam(userId, progress, fileName));
    }

    /**
     * 查询资产，判断是否重复
     *
     * @param userId
     * @param fileVO
     * @return
     */
    private TAssets checkAssets(Long userId, FileVO fileVO) {
        TAssets assets = new TAssets();
        String filePath = fileVO.getFilePath();
        String fileMD5  = analyzeMD5ByFileName(fileVO.getFileName());
        if (StringUtils.isEmpty(fileMD5)) {
            fileMD5 =  MD5Util.getFileMD5(filePath);
        }
        assets.setMd5(fileMD5);
        assets.setCreateUserId(userId);
        assets.setIsDelete(0);
        assets = findTassets(assets);
        return assets;
    }

    /**
     * 从文件名中解析MD5
     * @param fileName
     * @return
     */
    private String analyzeMD5ByFileName(String fileName) {
        if (!fileName.startsWith(ConstantsUtils.ASSET_SIGN)) {
            return null;
        }
        String root = fileName.replace(ConstantsUtils.ASSET_SIGN, "");
        return root.substring(0, root.indexOf("_"));
    }

    /**
     * 解析 包体信息
     *
     * @param fileVO
     * @param userId
     * @param isAbsolutePath
     */
    private TAssets analysis(FileVO fileVO, Long userId, boolean isAbsolutePath,String shellPath, String validTag) throws IjiamiApplicationException {
        TAssets assets = new TAssets();
        String bucketName = fileVO.getBucketName();
        if (!bucketName.endsWith(FileUtil.SEPARATOR)) {
            bucketName += FileUtil.SEPARATOR;
        }
        String fileMD5  = analyzeMD5ByFileName(fileVO.getFileName());
        if (StringUtils.isEmpty(fileMD5)) {
            fileMD5 =  MD5Util.getFileMD5(fileVO.getFilePath());
        }
        String bucketImages = bucketName + "images" + FileUtil.SEPARATOR;
        String iconFolder = fileVO.getFileUrl() + bucketImages;
        File file = new File(iconFolder);
        if (!file.exists()) {
            file.mkdirs();
        }

        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 30, validTag);

        //如果地址已经存在就不要上传到文件服务器
        if(org.apache.commons.lang3.StringUtils.isNoneBlank(shellPath)) {
            assets.setShellIpaPath(shellPath);
        }else{
            assets.setShellIpaPath(checkExistNdfsByMd5(fileMD5));
        }
        validAnalysisStatus(validTag, userId);
        // 解析apk
        if (ConstantsUtils.SUFFIX_APK.equalsIgnoreCase(fileVO.getFileExtName())) {
            analysisAPK(fileVO, userId, isAbsolutePath, assets, fileMD5, validTag);
        } else if (ConstantsUtils.SUFFIX_HAP.equalsIgnoreCase(fileVO.getFileExtName())) {
            analysisHap(fileVO, userId, isAbsolutePath, assets, fileMD5, validTag);
        } else {
            // 解释ios
            analysisIOS(fileVO, userId, isAbsolutePath, assets, fileMD5, validTag);
        }
        return assets;
    }

    private String checkExistNdfsByMd5(String fileMD5){
        List<TAssets> existAssetsList = assetsMapper.findByUserAndMd5(null,fileMD5);
        for (TAssets existAssets : existAssetsList) {
            if(StringUtils.isNotEmpty(existAssets.getShellIpaPath())){
                //检查文件是否失效
                HttpResponse execute = HttpUtil.createRequest(Method.HEAD, fastDFSIp + "/" + existAssets.getShellIpaPath()).execute();
                if(execute.getStatus()== HttpStatus.HTTP_OK){
                    execute.close();
                    return existAssets.getShellIpaPath();
                }
                execute.close();
            }
        }
        return null;
    }


    /**
     * 解析apk信息
     *
     * @param fileVO
     * @param userId
     * @param isAbsolutePath
     * @param assets
     * @param fileMD5
     * @throws IjiamiApplicationException
     */
    private void analysisAPK(FileVO fileVO, Long userId, boolean isAbsolutePath, TAssets assets, String fileMD5, String validTag)
            throws IjiamiApplicationException {
        String filePath = fileVO.getFilePath();
        try {
			String apkSignInfo = cn.ijiami.framework.apk.util.ApkUtil.getApkSign(filePath);
			if(StringUtils.isBlank(apkSignInfo)) {
				boolean sign = ApkV2SignInfo.getApkSingInfo(filePath);
				if(!sign) {
					throw new IjiamiApplicationException("应用未签名，请签名后再上传");
				}
	    	}
			LOG.info("签名信息:{}",apkSignInfo);
		} catch (Exception e3) {
			boolean sign = ApkV2SignInfo.getApkSingInfo(filePath);
			if(!sign) {
				throw new IjiamiApplicationException("应用未签名，请签名后再上传");
			}
		}

        Object size = fileVO.getParams().get("size");
        Long dateTime = System.currentTimeMillis();
        String bucketName = fileVO.getBucketName();
        if (!bucketName.endsWith(FileUtil.SEPARATOR)) {
            bucketName += FileUtil.SEPARATOR;
        }
        String bucketImages = bucketName + "images" + FileUtil.SEPARATOR;
        String iconFolder = fileVO.getFileUrl() + bucketImages;
        assets.setTerminalType(TerminalTypeEnum.ANDROID);
        AssetParserInfo info = parserAssetInfo(filePath);
        if (StringUtils.isBlank(info.getAppName())) {
        	throw new IjiamiApplicationException("读取APK信息失败，获取不到应用名称");
        }
        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 50, validTag);
        String fileName = dateTime + info.getAppName() + ConstantsUtils.SUFFIX_PNG;
        String iconPath = iconFolder + fileName;
        String RelativeIconPath = bucketImages + fileName;
        ApkIconUtils.extractFileFromApk(filePath, info.getIcon(), iconPath);
        assets.setName(info.getAppName());
        String apkVersion = info.getVersionName();
        // 新增需求去掉版本前面的V标志
        if (apkVersion != null) {
            apkVersion = apkVersion.toUpperCase().startsWith("V") ? apkVersion.substring(1) : apkVersion;
        }
        assets.setVersion(apkVersion);
        assets.setPakage(info.getPackageName());
        assets.setSize(String.valueOf(size));
        assets.setLogo(FileUtil.encodeData(RelativeIconPath));
        assets.setMd5(fileMD5);
        assets.setPermissions(CommonUtil.beanToJson(info.getUsesPermissions()));
        if (StringUtils.isNotBlank(info.getMinSdkVersion())) {
            assets.setMinSdkVersion(Integer.parseInt(info.getMinSdkVersion()));
        }
        if (StringUtils.isNotBlank(info.getTargetSdkVersion())) {
            assets.setTargetSdkVersion(Integer.parseInt(info.getTargetSdkVersion()));
        }
        assets.setSignMd5(cn.ijiami.framework.apk.analysis.SignApkInfoAnalysis.getAPPSignMD5(fileVO.getFilePath()));
        // 已经是绝对路径的，不需再拼接
        if (isAbsolutePath) {
            assets.setAddress(FileUtil.encodeData(fileVO.getRelativePath()));
        } else {
            assets.setAddress(FileUtil.encodeData(fileVO.getFilePath()));
        }
        //logo上传到文件服务
        uploadAssetsFile(fileVO, userId, assets, iconPath, validTag);
    }

    /**
     * 解析apk信息
     *
     * @param fileVO
     * @param isAbsolutePath
     * @param assets
     * @param fileMD5
     * @throws IjiamiApplicationException
     */
    private void analysisHap(FileVO fileVO, Long userId, boolean isAbsolutePath, TAssets assets, String fileMD5, String validTag)
            throws IjiamiApplicationException {
        String filePath = fileVO.getFilePath();
        String signInfo = extractAppSignature(filePath);
        if (StringUtils.isBlank(signInfo)) {
            throw new IjiamiApplicationException("应用未签名，请签名后再上传");
        }
        assets.setSignature(signInfo);
        Object size = fileVO.getParams().get("size");
        String bucketName = fileVO.getBucketName();
        if (!bucketName.endsWith(FileUtil.SEPARATOR)) {
            bucketName += FileUtil.SEPARATOR;
        }
        String bucketImages = bucketName + "images" + FileUtil.SEPARATOR;
        String iconFolder = fileVO.getFileUrl() + bucketImages;
        assets.setTerminalType(TerminalTypeEnum.HARMONY);
        HapInfo hapInfo;
        String appName = "";
        String hapIconPath = "";
        String versionName = "";
        String packageName = "";
        try {
            hapInfo = HapUtils.readHapInfo(filePath, iconFolder);
            if (hapInfo == null) {
                throw new IjiamiApplicationException("读取鸿蒙应用信息失败");
            }
            appName = hapInfo.getApp().getLabel();
            hapIconPath = hapInfo.getApp().getIcon();
            versionName = hapInfo.getApp().getVersionName();
            packageName = hapInfo.getApp().getBundleName();
        } catch (Exception e) {
            LOG.info("harmony fail {}", e.getMessage());
            throw new IjiamiApplicationException("读取鸿蒙应用信息失败");
        }

        if(org.apache.commons.lang3.StringUtils.isBlank(appName)) {
            throw new IjiamiApplicationException("读取鸿蒙应用信息失败，获取不到应用名称");
        }
        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 50, validTag);
        String relativeIconPath = bucketImages + CommonUtil.getFilenameFromURLIgnoringQuery(hapIconPath);

        assets.setName(appName);
        assets.setVersion(versionName);
        assets.setPakage(packageName);
        assets.setSize(String.valueOf(size));
        assets.setLogo(FileUtil.encodeData(relativeIconPath));
        assets.setMd5(fileMD5);

        assets.setSignMd5("");
        // 已经是绝对路径的，不需再拼接
        if (isAbsolutePath) {
            assets.setAddress(FileUtil.encodeData(fileVO.getRelativePath()));
        } else {
            assets.setAddress(FileUtil.encodeData(fileVO.getFilePath()));
        }
        if (hapInfo.getModule().getRequestPermissions() != null) {
            List<String> permissionNameList = hapInfo.getModule().getRequestPermissions()
                    .stream()
                    .map(HapInfo.ModuleDTO.RequestPermissionsDTO::getName)
                    .collect(Collectors.toList());
            assets.setPermissions(CommonUtil.beanToJson(permissionNameList));
        }
        if (Objects.nonNull(hapInfo.getApp().getTargetApiVersion())) {
            assets.setTargetSdkVersion(hapInfo.getApp().getTargetApiVersion());
        }
        if (Objects.nonNull(hapInfo.getApp().getMinApiVersion())) {
            assets.setMinSdkVersion(hapInfo.getApp().getMinApiVersion());
        }
        // 上传到文件服务
        uploadAssetsFile(fileVO, userId, assets, hapIconPath, validTag);
    }

    private void uploadAssetsFile(FileVO fileVO, Long userId, TAssets assets, String appIconPath, String validTag) throws IjiamiApplicationException {
        String imageUploadMethod = commonProperties.getProperty("image.upload.method");
        if (org.apache.commons.lang3.StringUtils.isNoneBlank(imageUploadMethod) && imageUploadMethod.equals("fdfs")) {
            try {
                FileVO imageLogo = uploadFileImage(appIconPath);
                if(imageLogo != null && org.apache.commons.lang3.StringUtils.isNoneBlank(imageLogo.getFileUrl())) {
                    assets.setLogo(commonProperties.getProperty("detection.result.url.prefix")+imageLogo.getFileUrl());
                }
            } catch (Exception e1) {
                LOG.error("upload icon fail", e1);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(assets.getShellIpaPath())) {
            FileVO appFastDfs = new FileVO();
            // apk安卓资产上传至文件服务器
            try (FileInputStream in = new FileInputStream(fileVO.getFilePath())){
                BeanUtils.copyProperties(fileVO, appFastDfs);
                appFastDfs.setInputStream(in);
                singleFastDfsFileService.instance().upload(appFastDfs);
            } catch (Exception e) {
                LOG.error("上传至文件服务器失败，失败原因：{}", e.getMessage(), e);
                throw new IjiamiApplicationException("上传至文件服务器失败"+e.getMessage());
            }
            if (StringUtils.isEmpty(appFastDfs.getFileUrl())) {
                throw new IjiamiApplicationException("上传至文件服务器失败，无数据返回");
            }
            assets.setShellIpaPath(appFastDfs.getFileUrl());
        }
        assets.setPlatform("");
        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 70, validTag);
    }

    private AssetParserInfo parserAssetInfo(String filePath) throws IjiamiApplicationException {
        AssetParserInfo info = new AssetParserInfo();
        try {
            ApkInfo apkInfo = cn.ijiami.detection.utils.APKUtil.readApkInfo(filePath);
            if (apkInfo != null) {
                info.setAppName(apkInfo.getApplicationLable());
                info.setIcon(apkInfo.getApplicationIcon());
                info.setVersionName(apkInfo.getVersionName());
                info.setPackageName(apkInfo.getPackageName());
                info.setMinSdkVersion(apkInfo.getMinSdkVersion());
                info.setTargetSdkVersion(apkInfo.getTargetSdkVersion());
                info.setUsesPermissions(apkInfo.getUsesPermissions());
            }
        } catch (Exception e) {
            LOG.info("apkInfo fail {}", e.getMessage());
        }
        if (StringUtils.isBlank(info.getAppName())
                || CollectionUtils.isEmpty(info.getUsesPermissions())
                || StringUtils.isBlank(info.getMinSdkVersion())
                || StringUtils.isBlank(info.getTargetSdkVersion())) {
            try {
                ApkMeta apkMeta = cn.ijiami.detection.utils.APKUtil.parserReadApkInfo(filePath);
                if (StringUtils.isBlank(info.getAppName())) {
                    info.setAppName(apkMeta.getName());
                }
                if (StringUtils.isBlank(info.getIcon())) {
                    info.setIcon(apkMeta.getIcon());
                }
                if (StringUtils.isBlank(info.getVersionName())) {
                    info.setVersionName(apkMeta.getVersionName());
                }
                if (StringUtils.isBlank(info.getPackageName())) {
                    info.setPackageName(apkMeta.getPackageName());
                }
                if (StringUtils.isBlank(info.getMinSdkVersion())) {
                    info.setMinSdkVersion(apkMeta.getMinSdkVersion());
                }
                if (StringUtils.isBlank(info.getTargetSdkVersion())) {
                    info.setTargetSdkVersion(apkMeta.getTargetSdkVersion());
                }
                if (CollectionUtils.isEmpty(info.getUsesPermissions())) {
                    info.setUsesPermissions(apkMeta.getUsesPermissions());
                }
            } catch (Exception e) {
                LOG.info("apkMeta fail {}", e.getMessage());
            }
            if (StringUtils.isBlank(info.getAppName())) {
                throw new IjiamiApplicationException("读取APK信息失败");
            }
        }
        return info;
    }
    
    private FileVO uploadFileImage(String filePath) {
        FileVO fileVO = new FileVO();
        try {
            File file = new File(filePath);
            // 文件扩展名
            FileInputStream inputStream = new FileInputStream(file);

            fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
            fileVO.setInputStream(inputStream);
            fileVO.setFileSize(file.length());
            fileVO.setFileName(file.getName());
            fileVO.setFilePath(filePath);
            fileVO = singleFastDfsFileService.instance().upload(fileVO);
        } catch (Exception e) {
            e.getMessage();
        }
        return fileVO;
    }

    /**
     * 解析ios资源
     *
     * @param fileVO
     * @param userId
     * @param isAbsolutePath
     * @param assets
     * @param fileMD5
     * @throws IjiamiApplicationException
     */
    private void analysisIOS(FileVO fileVO, Long userId, boolean isAbsolutePath, TAssets assets, String fileMD5, String validTag)
            throws IjiamiApplicationException {
        String filePath = fileVO.getFilePath();
        LOG.info("analysisIOS {}", filePath);
        Object size = fileVO.getParams().get("size");
        String bucketName = fileVO.getBucketName();
        if (!bucketName.endsWith(FileUtil.SEPARATOR)) {
            bucketName += FileUtil.SEPARATOR;
        }
        String bucketImages = bucketName + "images" + FileUtil.SEPARATOR;
        String iconFolder = fileVO.getFileUrl() + bucketImages;
        
        assets.setTerminalType(TerminalTypeEnum.IOS);
        long time1 = System.currentTimeMillis();
        //工具提供的解析太慢。提取到本地优化
//        IpaInfo ipaInfo = IpaUtil.readIpaInfo(filePath, iconFolder,iconFolder);
        IpaInfo ipaInfo = LocalIpaUtil.readIpaInfo(filePath, iconFolder,iconFolder);
        long time2 = System.currentTimeMillis();
        LOG.info("analysisIOS解析完成 {},耗时={}", filePath, (time2-time1));
        if (ipaInfo == null) {
            throw new IjiamiApplicationException("解析ipaInfo信息失败");
        }
        
        sendAnalysisProgress(userId, "文件解析", "正在解析文件...", 50, validTag);
        assets.setName(StringUtils.isEmpty(ipaInfo.getAppDisplayName()) ? ipaInfo.getAppName() : ipaInfo.getAppDisplayName());
        assets.setVersion(ipaInfo.getAppVersion());
        assets.setPakage(ipaInfo.getAppPackageName());
        assets.setSize(String.valueOf(size));
        assets.setLogo(FileUtil.encodeData(bucketImages + ipaInfo.getIcon()));
        assets.setMd5(fileMD5);
        assets.setSignMd5(ipaInfo.getAppSign());
        // 已经是绝对路径的，不需再拼接
        if (isAbsolutePath) {
            assets.setAddress(FileUtil.encodeData(fileVO.getRelativePath()));
        } else {
            assets.setAddress(FileUtil.encodeData(fileVO.getFilePath()));
        }
        // 是否需要脱壳
        boolean isHasShell;
        try {
        	long time3 = System.currentTimeMillis();
            String ipaAbsolutePath = FileUtil.decodeData(assets.getAddress());
            isHasShell = IpaShellInfoUtils.isHasShell(ipaAbsolutePath, commonProperties.getProperty("ijiami.shell.tool.path"));
            long time4 = System.currentTimeMillis();
            LOG.info("analysisIOS是否需要脱壳={},耗时={}", filePath, (time4-time3));
        } catch (Exception e) {
        	e.getMessage();
            throw new IjiamiApplicationException("上传文件或脱壳工具不存在..."+e.getMessage());
        }
        assets.setIsHavePacker(isHasShell ? PackerStatusEnum.SHELL.getValue() : PackerStatusEnum.SHELL_LESS.getValue());
        //有壳情况
        long time5 = System.currentTimeMillis();
        if (isHasShell) {
            String appId = IpaShellInfoUtils.getAppIdByBundleId(ipaInfo.getAppPackageName());
            assets.setAppId(appId);
        } else {
        	//无壳情况,linux重签工具直接重签
        }
        long time6 = System.currentTimeMillis();
        LOG.info("analysisIOS有壳情况={},耗时={}", filePath, (time6-time5));
        // 上传到文件服务
        uploadAssetsFile(fileVO, userId, assets, ipaInfo.getIcon(), validTag);
        long time7 = System.currentTimeMillis();
        LOG.info("analysisIOS上传到文件服务={},耗时={}", filePath, (time7-time6));
    }

    @Override
    public TAssets findTassets(TAssets assets) {
        List<TAssets> assetsList = assetsMapper.select(assets);
        return (assetsList != null && assetsList.size() > 0) ? assetsList.get(0) : null;
    }

    @Override
    public TAssets getAssetsById(Long assetsId) {
        return assetsMapper.selectByPrimaryKey(assetsId);
    }

    @Override
    @CacheEvict(value = "privacy-detection:assets", allEntries = true)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public TAssets saveOrUpdate(TAssets assets) throws IjiamiCommandException {
        if (assets.getCategory() == null) {
            List<TApkCategory> categorys = apkCategoryMapper.select(new TApkCategory());
            assets.setCategory(categorys.get(0).getCategoryId());
        }
        if(org.apache.commons.lang3.StringUtils.isNoneBlank(assets.getLogo()) && assets.getLogo().startsWith("http")) {
        	assets.setLogo(assets.getLogo().replace(commonProperties.getProperty("detection.result.url.prefix"),""));
        }
        if (assets.getTerminalType().getValue() == TerminalTypeEnum.IOS.getValue()
                || assets.getTerminalType().getValue() == TerminalTypeEnum.ANDROID.getValue()) {
            // 判断资产是否有效
            if (StringUtils.isEmpty(assets.getShellIpaPath())) {
                throw new IjiamiCommandException(assets.getName() + "该资产无有效的文件地址");
            }
        }
        if (StringUtils.isBlank(assets.getPrivacyPolicyPath())) {
            TStorageLog privacyPolicyStorageLog = storageLogService.findOneByAssociationKey(assets.getMd5(),
                    Collections.singletonList(StorageFileType.TXT), assets.getCreateUserId());
            if (Objects.nonNull(privacyPolicyStorageLog)) {
                // 添加用户上传隐私文件的地址
                assets.setPrivacyPolicyPath(privacyPolicyStorageLog.getStorageAddress());
            } else {
                assets.setPrivacyPolicyPath(StringUtils.EMPTY);
            }
        }
        if (StringUtils.isBlank(assets.getThirdPartyShareListPath())) {
            TStorageLog thirdPartyShareStorageLog = storageLogService.findOneByAssociationKey(assets.getMd5(),
                    Collections.singletonList(StorageFileType.XLSX), assets.getCreateUserId());
            if (Objects.nonNull(thirdPartyShareStorageLog)) {
                // 添加用户上传的第三方共享清单地址
                assets.setThirdPartyShareListPath(thirdPartyShareStorageLog.getStorageAddress());
            } else {
                assets.setThirdPartyShareListPath(StringUtils.EMPTY);
            }
        }
        Date date = new Date();
        TAssets oldAssets = assetsMapper.findOneByUserAndMd5(assets.getCreateUserId(), assets.getMd5());
        // 如果是新添加的资产，要检查这个人有没有上传过同样的资产，如果有改为更新。因为同个人的相同资产是合并显示的，只显示最开始那个
        if (assets.getId() == null && oldAssets != null) {
            assets.setId(oldAssets.getId());
        }
        
        
        //前端勾选不需要脱壳或者重签 20240925
        if(assets.getIsNeedSign()!= null && assets.getIsNeedSign() ==2){
        	assets.setIsHavePacker(PackerStatusEnum.RESIGN.getValue());
        	assets.setDumpZipUrl(assets.getShellIpaPath()); //如果不传扫码检测就异常
        }
        
        if (assets.getId() != null) {
            assets.setUpdateTime(date);
            assetsMapper.updateByPrimaryKeySelective(assets);
        } else {
            assets.setCreateTime(date);
            assets.setUpdateTime(date);
            assets.setIsDelete(0);
            assetsMapper.insertSelective(assets);
            
//            //保存静态广播
//            List<TBroadcast> broadcast = assets.getBroadcastList();
//            LOG.info("保存静态广播={}",broadcast==null?0:broadcast.size());
//            if(broadcast != null && broadcast.size()>0 && assets.getId()!=null){
//            	broadcast.forEach(b->{
//            		b.setAssetsId(assets.getId());
//            		broadcastMapper.insert(b);
//            	});
////            	broadcastMapper.insertList(broadcast);
//            }
        }

        //无壳调用Linxu重签，有壳继续之前的流程2023-05-22
        if (assets.getTerminalType().getValue() == TerminalTypeEnum.IOS.getValue() && StringUtils.isBlank(assets.getDumpZipUrl())
                && Objects.nonNull(assets.getIsHavePacker())
                && assets.getIsHavePacker() == PackerStatusEnum.SHELL_LESS.getValue() && StringUtils.isBlank(assets.getTestflightUrl())) {
            iosResignThread(assets);
        }
        
        
      //无壳调用Linxu重签，有壳继续之前的流程2023-05-22
        if (assets.getTerminalType().getValue() == TerminalTypeEnum.IOS.getValue() && StringUtils.isBlank(assets.getDumpZipUrl())
        		&& Objects.nonNull(assets.getIsHavePacker())
        		&& assets.getIsHavePacker() == PackerStatusEnum.SHELL_LESS.getValue() && 
        		(assets.getIsNeedSign()== null || assets.getIsNeedSign() !=2)) {
        	iosResignThread(assets);
        }
        
        // 更新文件存储记录表
        storageLogService.updateStorageLog(assets);
        // 更新分片文件使用状态
        if(assets.getUploadFileId() != null){
            TChunkUploadFile update = new TChunkUploadFile();
            update.setId(assets.getUploadFileId());
            update.setStatus(ChunkUploadFileStatus.USED);
            chunkUploadFileMapper.updateByPrimaryKeySelective(update);
        }
        startExtractAppInfoTask(assets);
        return assets;
    }
    
    /**
     * ipa开始重签
     */
    private void iosResignThread(TAssets assets){
    	
    	if(StringUtils.isNotBlank(commonProperties.getProperty("ios.resign.enabled")) && commonProperties.getProperty("ios.resign.enabled").equals("false")) {
    		return;
    	}
    	
    	//检测结束，推送检测结果
  		FixedThreadPoolManager fixed = FixedThreadPoolManager.getInstance();
  		IOSResignThread thread = new IOSResignThread(assets, commonProperties, singleFastDfsFileService, assetsMapper, fastDFSIntranetIp);
		fixed.execute(thread);
    }

    @Override
    @CacheEvict(value = "privacy-detection:assets", allEntries = true)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void saveOrUpdateList(List<TAssets> assetsList, TerminalTypeEnum typeEnum, Long userId) throws IjiamiApplicationException, IjiamiCommandException {
        for (TAssets assets : assetsList) {
            assets.setUpdateUserId(userId);
            assets.setCreateUserId(userId);
            assets.setTerminalType(typeEnum);
            saveOrUpdate(assets);
        }
    }

    @Override
    @CacheEvict(value = "privacy-detection:assets", allEntries = true)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void saveOrUpdateListByFileVO(List<FileVO> fileVOList, Long userId) throws IjiamiApplicationException, IjiamiCommandException {
        for (FileVO fileVO : fileVOList) {
            TAssets assets = analysisApk(fileVO, false, userId, false);
            assets.setUpdateUserId(userId);
            assets.setCreateUserId(userId);
            saveOrUpdate(assets);
            // 保存图片信息
            fileVO.setFileKey(assets.getLogo());
            fileVO.setRelativePath(FileUtil.decodeData(assets.getLogo()));
            fileVO.setFileExtName(
                    fileVO.getRelativePath().substring(fileVO.getRelativePath().lastIndexOf(".") + 1).toUpperCase());
            fileVO.setFileName(fileVO.getRelativePath().substring(fileVO.getRelativePath().lastIndexOf("/") + 1));
            fileVO.setFileSize(0L);
            fileService.saveFile(fileVO);
        }
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Override
    public void urlUpload(UrlUploadVO urlUploadVO, HttpServletResponse response, HttpServletRequest request, Long userId) throws Exception {
        String md5 = MD5Util.encode(urlUploadVO.getUrl(),"");
        FileVO fileVO = downFileSendMessage(md5, urlUploadVO, userId);
        validAnalysisStatus(md5, userId);
        TAssets assets = analysisApkInternal(fileVO, userId, false, null, md5);
        validAnalysisStatus(md5, userId);
        UrlMessageVO messageVO = new UrlMessageVO();
        BeanUtils.copyProperties(assets, messageVO);
        messageVO.setUnitProgress(100);
        sendAnalysisProgressWithMessageVO(userId, "文件解析", "解析完成...", messageVO);
    }

    @Override
    public CheckChunkFileVO checkChunkFile(String fileMd5, String sourceFileName, Integer chunkTotal, Long userId) throws Exception {
        LOG.info("检查分片上传 fileMd5={} sourceFileName={} chunkTotal={} userId={}", fileMd5, sourceFileName, chunkTotal, userId);
        // 先找是否有已经上传的
        TChunkUploadFile uploadFinishFile = chunkUploadFileMapper.findOneByUserIdAndStatus(userId,fileMd5,
                Arrays.asList(ChunkUploadFileStatus.FINISH.itemValue(), ChunkUploadFileStatus.ANALYZING.itemValue(), ChunkUploadFileStatus.ANALYSIS_OK.itemValue()));
        CheckChunkFileVO checkChunkFileVO = new CheckChunkFileVO();
        if (uploadFinishFile != null) {
            checkChunkFileVO.setChunkTotal(uploadFinishFile.getChunkTotal());
            checkChunkFileVO.setFileMd5(uploadFinishFile.getFileMd5());
            checkChunkFileVO.setCompleted(true);
        }
        return checkChunkFileVO;
    }


    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Override
    public CheckChunkFileVO chunkUpload(MultipartFile chunkFile, String fileName, String fileMd5, String chunkMd5, Integer chunkNumber,
                                        Integer chunkTotal, Long userId) throws Exception {
        LOG.info("开始分片上传 fileMd5={} fileName={} chunkMd5={} chunkNumber{} chunkTotal={} userId={}", fileMd5, fileName, chunkMd5, chunkNumber, chunkTotal, userId);
        CommonUtil.checkAppFileExt(fileName);
        TChunkUploadFile chunkUploadFile = findUploadFileInfo(fileMd5, chunkMd5, chunkNumber, chunkTotal, userId);
        // 判断分片是否都上传了
        TChunkUploadPart query = new TChunkUploadPart();
        query.setFileId(chunkUploadFile.getId());
        List<TChunkUploadPart> saveList = chunkUploadPartMapper.select(query);
        // 检查参数是否正确，错误需要重新上传
        if (!chunkTotal.equals(chunkUploadFile.getChunkTotal())) {
            setUploadChunkFileFailure(chunkUploadFile.getId());
            clearChunkFilePart(saveList);
            throw new IjiamiApplicationException("上传参数错误");
        }
        // 判断分片是否上传过了
        Set<String> chunkSet = saveList.stream().map(TChunkUploadPart::getChunkMd5).collect(Collectors.toSet());
        if (chunkSet.contains(chunkMd5)) {
            LOG.info("已经上传过了");
            if (saveList.size() == chunkUploadFile.getChunkTotal()) {
                return processChunkFileMerge(saveList, chunkUploadFile, fileName, fileMd5, chunkMd5, chunkTotal);
            } else {
                return makeUploadingResult(saveList, fileMd5, chunkMd5, chunkTotal);
            }
        }
        // 单个的情况需要加文件后缀
        File saveFile = multipartFileSaveToDisk(chunkFile, getDiskChunkFile(
                chunkTotal == 1 ? getSingleFileName(fileMd5, FileVOUtils.findFileExtName(fileName)) : chunkMd5));
        FileVO uploadFile = FileVOUtils.convertFileVOByFile(saveFile);
        FileVO fileVO = singleFastDfsFileService.instance().storeFile(uploadFile);
        LOG.info("上传到FastDFs filePath={}", fileVO.getFilePath());
        if (StringUtils.isEmpty(fileVO.getFilePath())) {
            return makeRestartPartResult(saveList, fileMd5, chunkMd5, chunkTotal);
        }
        TChunkUploadPart savePart = new TChunkUploadPart();
        savePart.setFileId(chunkUploadFile.getId());
        savePart.setChunkFilePath(fileVO.getFilePath());
        savePart.setChunkSize(chunkFile.getSize());
        savePart.setChunkMd5(chunkMd5);
        savePart.setChunkNum(chunkNumber);
        savePart.setCreateTime(new Date());
        savePart.setUpdateTime(new Date());
        chunkUploadPartMapper.insert(savePart);
        // 重新查一遍，避免因为并发上传导致其他分片已经上传完成却不知道
        List<TChunkUploadPart> newSaveList = chunkUploadPartMapper.select(query);
        if (newSaveList.size() == chunkUploadFile.getChunkTotal()) {
            return processChunkFileMerge(newSaveList, chunkUploadFile, fileName, fileMd5, chunkMd5, chunkTotal);
        } else {
            return makeUploadingResult(newSaveList, fileMd5, chunkMd5, chunkTotal);
        }
    }

    @Override
    public CheckChunkFileVO chunkUpload_new(MultipartFile chunkFile, String fileName, String fileMd5, String chunkMd5, Integer chunkNumber,
                                        Integer chunkTotal, Long userId,Boolean autoStore) throws Exception {
        CheckChunkFileVO res = new CheckChunkFileVO();
        res.setUploadChunkStatus(1);
        LOG.info("开始分片上传 fileMd5={} fileName={} chunkMd5={} chunkNumber{} chunkTotal={} userId={}", fileMd5, fileName, chunkMd5, chunkNumber, chunkTotal, userId);
        CommonUtil.checkAppFileExt(fileName);
        TChunkUploadFile chunkUploadFile = findUploadFileInfo_new(fileName, fileMd5, chunkNumber, chunkTotal, userId,autoStore);
        // 判断分片是否都上传了
        TChunkUploadPart query = new TChunkUploadPart();
        query.setFileId(chunkUploadFile.getId());
        List<TChunkUploadPart> uploadPartList = chunkUploadPartMapper.select(query);
        // 检查参数是否正确，错误需要重新上传
        if (!chunkTotal.equals(chunkUploadFile.getChunkTotal())) {
            setUploadChunkFileFailure(chunkUploadFile.getId());
            clearChunkFilePart(uploadPartList);
            throw new IjiamiApplicationException("上传分片数错误");
        }
        File partFile = getDiskChunkFile(chunkTotal==1?fileName:chunkMd5);

        try {
            //避免之前的包有问题。每次都重新覆盖
            Files.deleteIfExists(partFile.toPath());
            chunkFile.transferTo(partFile);

            Set<String> chunkSet = uploadPartList.stream().map(TChunkUploadPart::getChunkMd5).collect(Collectors.toSet());
            if (!chunkSet.contains(chunkMd5)) {
                LOG.info("分片入库");
                TChunkUploadPart savePart = new TChunkUploadPart();
                savePart.setFileId(chunkUploadFile.getId());
                savePart.setChunkFilePath(partFile.getAbsolutePath());
                savePart.setChunkSize(chunkFile.getSize());
                savePart.setChunkMd5(chunkMd5);
                savePart.setChunkNum(chunkNumber);
                savePart.setCreateTime(new Date());
                savePart.setUpdateTime(new Date());
                chunkUploadPartMapper.insertSelective(savePart);
            }
        }catch (Exception e){
            LOG.error("分片保存失败",e);
            res.setUploadChunkStatus(2);
            return res;
        }
        // 更新状态
        List<TChunkUploadPart> newSaveList = chunkUploadPartMapper.select(query);
        if (newSaveList.size() == chunkUploadFile.getChunkTotal()) {
            boolean flag = chunkUploadFileMapper.updateFileStatusFrom(chunkUploadFile.getId(), ChunkUploadFileStatus.FINISH.itemValue(), Collections.singletonList(ChunkUploadFileStatus.UPLOADING.itemValue()));
            if (flag) {
                prepareChunkFileMerge();
            }
        }
        return res;
    }

    private void prepareChunkFileMerge() {
        Integer count = chunkUploadFileMapper.selectCountByStatus(ChunkUploadFileStatus.ANALYZING.itemValue());
        LOG.info("当前分片合并任务:{}",count);
        TChunkUploadFile query = new TChunkUploadFile();
        query.setStatus(ChunkUploadFileStatus.FINISH);
        for (int i = count; i < 4; i++) {
            TChunkUploadFile chunkUploadFile = chunkUploadFileMapper.selectOneByStatus(ChunkUploadFileStatus.FINISH.itemValue());
            if(null == chunkUploadFile){
                break;
            }
            List<TChunkUploadPart> uploadPartList = chunkUploadPartMapper.selectByFileId(chunkUploadFile.getId());
            boolean flag = chunkUploadFileMapper.updateFileStatusFrom(chunkUploadFile.getId(), ChunkUploadFileStatus.ANALYZING.itemValue(), Collections.singletonList(ChunkUploadFileStatus.FINISH.itemValue()));
            if (flag) {
                new Thread(() -> processChunkFileMerge_new(chunkUploadFile,uploadPartList)).start();
            }
        }
    }

    @Override
    public void stopUrlUpload(String url, Long userId) {
        cacheService.set(PinfoConstant.CACHE_ANALYSIS_STOP + userId, STOP_FLAG, 10L, TimeUnit.MINUTES);
    }

    @Override
    public void stopChunkUpload(String md5, Long userId) {
        TChunkUploadFile entity = chunkUploadFileMapper.findOneByUserIdAndStatus(userId, md5,
                Arrays.asList(ChunkUploadFileStatus.ANALYZING.itemValue(), ChunkUploadFileStatus.UPLOADING.itemValue(),
                        ChunkUploadFileStatus.FINISH.itemValue(), ChunkUploadFileStatus.USED.itemValue(), ChunkUploadFileStatus.ANALYSIS_OK.itemValue()));
        if (entity != null) {
            LOG.info("stopChunkUpload id={}", entity.getId());
            cacheService.set(PinfoConstant.CACHE_ANALYSIS_STOP + userId+entity.getFileMd5(), STOP_FLAG, 10L, TimeUnit.MINUTES);
            TChunkUploadFile update = new TChunkUploadFile();
            update.setId(entity.getId());
            update.setStatus(ChunkUploadFileStatus.DELETE);
            chunkUploadFileMapper.updateByPrimaryKeySelective(update);
        }
    }

    private CheckChunkFileVO processChunkFileMerge(List<TChunkUploadPart> saveList, TChunkUploadFile chunkUploadFile,
                                                   String fileName, String fileMd5, String chunkMd5, Integer chunkTotal) throws IjiamiApplicationException {
        // 检查文件大小是否超出
        long uploadSize = saveList.stream().map(TChunkUploadPart::getChunkSize).count();
        float uploadSizeMB = uploadSize / 1024f / 1024f;
        long maximumFileSize = Long.parseLong(commonProperties.getProperty("ijiami.upload.maximumFileSize"));
        if (uploadSizeMB > maximumFileSize) {
            throw new IjiamiApplicationException("文件太大 total=" + uploadSizeMB + "MB maximumFileSize=" + maximumFileSize + "MB");
        }

        TChunkUploadFile updateFile = new TChunkUploadFile();
        updateFile.setId(chunkUploadFile.getId());
        // 处理单个的情况
        if (chunkTotal == 1) {
            TChunkUploadPart part = saveList.get(0);
            FileVO singleFile = getPartFileVO(part, getSingleFileName(fileMd5, FileVOUtils.findFileExtName(fileName)));
            // 保存合并后的文件信息
            updateFile.setDfsPath(part.getChunkFilePath());
            updateFile.setFileName(singleFile.getFileName());
            updateFile.setFileBucketName(singleFile.getBucketName());
            updateFile.setFilePathPrefix(singleFile.getFileUrl());
            updateFile.setFileSize(singleFile.getFileSize());
        } else {
            FileVO saveFile;
            try {
                saveFile = mergePart(createSaveFileVO(FileVOUtils.findFileExtName(fileName)), saveList);
            } catch (ChunkFileMergeException e) {
                // 把上传状态更新为失败
                setUploadChunkFileFailure(chunkUploadFile.getId());
                clearChunkFilePart(saveList);
                return makeRestartAllResult(chunkUploadFile, chunkMd5);
            }
            FileVO uploadFile = convertUploadFileVOByFile(saveFile);
            singleFastDfsFileService.instance().storeFile(uploadFile);
            // 保存合并后的文件信息
            updateFile.setDfsPath(uploadFile.getFilePath());
            updateFile.setFileName(saveFile.getFileName());
            updateFile.setFileBucketName(saveFile.getBucketName());
            updateFile.setFilePathPrefix(saveFile.getFileUrl());
            updateFile.setFileSize(saveFile.getFileSize());
        }
        updateFile.setStatus(ChunkUploadFileStatus.FINISH);
        updateFile.setUpdateTime(new Date());
        chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
        // 上传完成
        CheckChunkFileVO checkChunkFileVO = new CheckChunkFileVO();
        checkChunkFileVO.setFileMd5(chunkUploadFile.getFileMd5());
        checkChunkFileVO.setUploadChunkMd5(chunkMd5);
        checkChunkFileVO.setChunkTotal(chunkUploadFile.getChunkTotal());
        checkChunkFileVO.setCompleted(true);
        checkChunkFileVO.setUploadChunkStatus(UploadChunkStatus.SUCCESS.itemValue());
        return checkChunkFileVO;
    }

    private void processChunkFileMerge_new(TChunkUploadFile chunkUploadFile,List<TChunkUploadPart> partList) {
        TChunkUploadFile updateFile = new TChunkUploadFile();
        updateFile.setId(chunkUploadFile.getId());
        try {
            File destFile = getDiskChunkFile(chunkUploadFile.getFileName());
            updateFile.setFileLocalPath(destFile.getAbsolutePath());
            long time = System.currentTimeMillis();
            LOG.info("分片合并开始={}",chunkUploadFile.getFileName());
            //单个的情况
            if (chunkUploadFile.getChunkTotal() == 1) {
                updateFile.setFileSize(partList.get(0).getChunkSize());
            }else{
                List<String> paths = partList.stream().map(TChunkUploadPart::getChunkFilePath).collect(Collectors.toList());
                mergePart_new(destFile,paths);
                updateFile.setFileSize(destFile.length());
            }
            LOG.info("分片合并结束={},耗时={}",chunkUploadFile.getFileName(),(System.currentTimeMillis()-time));
            updateFile.setProgress(20);
            updateFile.setMessage("文件合并完成");
            updateFile.setUpdateTime(new Date());
            chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
            //删除
            clearChunkFilePart(partList);
            //开始解析
            FileVO fileVO = convertDiskFileVO(chunkUploadFile.getFilePathPrefix(), chunkUploadFile.getFileBucketName(), chunkUploadFile.getFileName(), updateFile);
            setFileVOSize(fileVO, destFile.length());

            time = System.currentTimeMillis();
            LOG.info("应用包解析开始={}",chunkUploadFile.getFileName());
            TAssets assets = analysisApkInternal(fileVO, chunkUploadFile.getUserId(), false, null, chunkUploadFile.getFileName());
            LOG.info("应用包解析结束={},耗时={}",chunkUploadFile.getFileName(),(System.currentTimeMillis()-time));
            assets.setSourceFileName(chunkUploadFile.getFileName());
            assets.setCreateUserId(chunkUploadFile.getUserId());
            assets.setUpdateUserId(chunkUploadFile.getUserId());
            assets.setUploadFileId(chunkUploadFile.getId());
            // 解析完成后调用大数据平台接口获取资产功能类型
            assetsOfBigDataService.getAssetsBigData(assets);

            chunkUploadFile=chunkUploadFileMapper.selectByPrimaryKey(chunkUploadFile.getId());
            if(chunkUploadFile.getStatus()==ChunkUploadFileStatus.ANALYZING){
                updateFile.setProgress(100);
                updateFile.setMessage("解析完成");
                updateFile.setUpdateTime(new Date());
                updateFile.setStatus(ChunkUploadFileStatus.ANALYSIS_OK);
                updateFile.setDfsPath(assets.getShellIpaPath());
                updateFile.setAnalysisResult(beanToJson(assets));
                chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
                sendUploadMessage(chunkUploadFile.getUserId(),100,chunkUploadFile.getFileName());
            }
            if(chunkUploadFile.getStatus()==ChunkUploadFileStatus.ANALYZING && chunkUploadFile.getAutoStore()){
                assets.setCategory(4L);
                saveOrUpdate(assets);
            }

        }catch (Exception e){
            LOG.error("文件解析失败:{}",chunkUploadFile,e);
            updateFile.setStatus(ChunkUploadFileStatus.ANALYSIS_FAIL);
            updateFile.setMessage(e.getMessage());
            updateFile.setUpdateTime(new Date());
            chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
            sendUploadMessage(chunkUploadFile.getUserId(),50,chunkUploadFile.getFileName());
        }finally {
            prepareChunkFileMerge();
        }

    }

    /**
     * 正在上传中
     * @param saveList
     * @param fileMd5
     * @param chunkMd5
     * @param chunkTotal
     * @return
     */
    private CheckChunkFileVO makeUploadingResult(List<TChunkUploadPart> saveList, String fileMd5, String chunkMd5, Integer chunkTotal) {
        CheckChunkFileVO checkChunkFileVO = new CheckChunkFileVO();
        checkChunkFileVO.setCompleted(false);
        checkChunkFileVO.setFileMd5(fileMd5);
        checkChunkFileVO.setUploadChunkMd5(chunkMd5);
        checkChunkFileVO.setChunkTotal(chunkTotal);
        checkChunkFileVO.setUploadChunkStatus(UploadChunkStatus.SUCCESS.itemValue());
        checkChunkFileVO.setChunkFileList(saveList.stream().map(part -> {
            CheckChunkFileVO.ChunkFile chunk = new CheckChunkFileVO.ChunkFile();
            chunk.setChunkMd5(part.getChunkMd5());
            chunk.setChunkNumber(part.getChunkNum());
            chunk.setChunkSize(part.getChunkSize());
            return chunk;
        }).collect(Collectors.toList()));
        return checkChunkFileVO;
    }

    /**
     * 返回单个分块重新上传的错误
     * @param saveList
     * @param fileMd5
     * @param chunkMd5
     * @param chunkTotal
     * @return
     */
    private CheckChunkFileVO makeRestartPartResult(List<TChunkUploadPart> saveList, String fileMd5, String chunkMd5, Integer chunkTotal) {
        CheckChunkFileVO checkChunkFileVO = new CheckChunkFileVO();
        checkChunkFileVO.setCompleted(false);
        checkChunkFileVO.setFileMd5(fileMd5);
        checkChunkFileVO.setUploadChunkMd5(chunkMd5);
        checkChunkFileVO.setChunkTotal(chunkTotal);
        checkChunkFileVO.setUploadChunkStatus(UploadChunkStatus.UPLOAD_ERROR.itemValue());
        checkChunkFileVO.setChunkFileList(saveList.stream().map(part -> {
            CheckChunkFileVO.ChunkFile chunk = new CheckChunkFileVO.ChunkFile();
            chunk.setChunkMd5(part.getChunkMd5());
            chunk.setChunkNumber(part.getChunkNum());
            chunk.setChunkSize(part.getChunkSize());
            return chunk;
        }).collect(Collectors.toList()));
        return checkChunkFileVO;
    }

    /**
     * 返回全部分块重新上传的错误
     * @param chunkUploadFile
     * @param chunkMd5
     * @return
     */
    private CheckChunkFileVO makeRestartAllResult(TChunkUploadFile chunkUploadFile, String chunkMd5) {
        CheckChunkFileVO checkChunkFileVO = new CheckChunkFileVO();
        checkChunkFileVO.setFileMd5(chunkUploadFile.getFileMd5());
        checkChunkFileVO.setCompleted(false);
        checkChunkFileVO.setUploadChunkMd5(chunkMd5);
        checkChunkFileVO.setChunkTotal(chunkUploadFile.getChunkTotal());
        checkChunkFileVO.setUploadChunkStatus(UploadChunkStatus.MERGE_ERROR.itemValue());
        return checkChunkFileVO;
    }

    private String getMergeFileName(String fileNameSuffix) {
        return UuidUtil.uuid() + "." + fileNameSuffix;
    }

    private String getSingleFileName(String chunkMd5, String fileNameSuffix) {
        return chunkMd5 + "." + fileNameSuffix;
    }


    /**
     * 本机磁盘保存一份，合并的时候如果本机都有就不用下载
     */
    private File multipartFileSaveToDisk(MultipartFile file, File saveFile) {
        try {
            file.transferTo(saveFile);
            return saveFile;
        } catch (IOException e) {
            LOG.error("文件分片保存到磁盘失败", e);
            return null;
        }
    }

    private File getDiskChunkFile(String fileName) {
//        return new File(commonProperties.getProperty("ijiami.framework.file.path") + "/default/", fileName);
        File chunkFile = new File(fileExtractPath + "default/", fileName);
        File parentDir = chunkFile.getParentFile();
        // 如果父目录不存在
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        return chunkFile;
    }

    private FileVO createSaveFileVO(String fileNameSuffix) {
        String fileName = getMergeFileName(fileNameSuffix);
        FileVO fileVO = new FileVO();
        String bucketName = "/default/";
        fileVO.setBucketName(bucketName);
        fileVO.setFileUrl(defaultService.getFileContext().getFilePath());
        fileVO.setFilePath(fileVO.getFileUrl() + bucketName + fileName);
        fileVO.setFileName(fileName);
        fileVO.setFileExtName(fileNameSuffix);
        return fileVO;
    }

    private FileVO convertDiskFileVO(String pathPrefix, String bucketName, String fileName, TChunkUploadFile chunkUploadFile) {
        String fileExtName = FileVOUtils.findFileExtName(fileName);
        FileVO fileVO = new FileVO();
        fileVO.setBucketName(bucketName);
        fileVO.setFileUrl(pathPrefix);
//        fileVO.setFilePath(pathPrefix + bucketName + fileName);
        fileVO.setFilePath(chunkUploadFile.getFileLocalPath());
        fileVO.setFileName(fileName);
        fileVO.setFileExtName(fileExtName);
        return fileVO;
    }

    private void setFileVOSize(FileVO fileVO, Long fileSize) {
        fileVO.setFileSize(fileSize);
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) fileVO.getFileSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
    }

    private FileVO convertUploadFileVOByFile(FileVO file)  {
        FileVO fileVO = new FileVO();
        fileVO.setFilePath(file.getFilePath());
        fileVO.setFileSize(file.getFileSize());
        fileVO.setFileName(file.getFileName());
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) file.getFileSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
        return fileVO;
    }

    private TChunkUploadFile findUploadFileInfo(String fileMd5, String chunkMd5, Integer chunkNumber, Integer chunkTotal, Long userId) {
        String key = DistributedLockConstant.KEY_USER_CHUNK_UPLOAD_PREFIX + fileMd5 + userId;
        if (!distributedLockService.tryLock(key, TimeUnit.SECONDS.toMillis(5))) {
            //获取不到其他线程很可能已经写入了
            TChunkUploadFile chunkUploadFile = chunkUploadFileMapper.findOneByUserIdAndStatus(userId, fileMd5,
                    Collections.singletonList(ChunkUploadFileStatus.UPLOADING.itemValue()));
            if (chunkUploadFile != null) {
                return chunkUploadFile;
            }

            LOG.error("获取分布式锁失败");
            throw new IjiamiRuntimeException("上传任务创建失败");
        }
        try {
            TChunkUploadFile chunkUploadFile = chunkUploadFileMapper.findOneByUserIdAndStatus(userId, fileMd5,
                    Collections.singletonList(ChunkUploadFileStatus.UPLOADING.itemValue()));
            if (chunkUploadFile == null) {
                chunkUploadFile = new TChunkUploadFile();
                chunkUploadFile.setFileMd5(fileMd5);
                chunkUploadFile.setUserId(userId);
                chunkUploadFile.setChunkTotal(chunkTotal);
                chunkUploadFile.setFileName("");
                chunkUploadFile.setFilePathPrefix("");
                chunkUploadFile.setFileBucketName("");
                chunkUploadFile.setDfsPath("");
                chunkUploadFile.setFileSize(0L);
                chunkUploadFile.setStatus(chunkTotal > 1 ? ChunkUploadFileStatus.UPLOADING : ChunkUploadFileStatus.FINISH);
                chunkUploadFile.setCreateTime(new Date());
                chunkUploadFile.setUpdateTime(new Date());
                chunkUploadFileMapper.insert(chunkUploadFile);
                LOG.info("第一个分片上传成功 fileMd5={} chunkMd5={} chunkNumber{} chunkTotal={} userId={}",
                        fileMd5, chunkMd5, chunkNumber, chunkTotal, userId);
                cleanAnalysisStatus(fileMd5, userId);
            }
            return chunkUploadFile;
        } finally {
            distributedLockService.unlock(key);
        }
    }

    private TChunkUploadFile findUploadFileInfo_new(String fileName, String fileMD5, Integer chunkNumber, Integer chunkTotal, Long userId,Boolean autoStore) {
        TChunkUploadFile chunkUploadFile;
        //加锁新增任务
        String key = DistributedLockConstant.KEY_USER_CHUNK_UPLOAD_PREFIX + fileName + userId;
        if (!distributedLockService.tryLock(key, TimeUnit.SECONDS.toMillis(5))) {
            //获取不到其他线程很可能已经写入了
            chunkUploadFile = chunkUploadFileMapper.findOneByFileNameOrMd5(userId, fileName,null,Arrays.asList(0));
            if (chunkUploadFile != null) {
                return chunkUploadFile;
            }
            LOG.error("获取分布式锁失败");
            throw new IjiamiRuntimeException("上传任务创建失败");
        }
        try {
            chunkUploadFile = chunkUploadFileMapper.findOneByFileNameOrMd5(userId, fileName,null,Arrays.asList(0));
            if (chunkUploadFile == null) {
                chunkUploadFile = new TChunkUploadFile();
                chunkUploadFile.setFileMd5(fileMD5);
                chunkUploadFile.setUserId(userId);
                chunkUploadFile.setChunkTotal(chunkTotal);
                chunkUploadFile.setFileName(fileName);
                chunkUploadFile.setFilePathPrefix(commonProperties.getProperty("ijiami.framework.file.path")); //TODO
//                chunkUploadFile.setFilePathPrefix(fileExtractPath);
                chunkUploadFile.setFileBucketName("/default/");
                chunkUploadFile.setDfsPath("");
                chunkUploadFile.setFileSize(0L);
                chunkUploadFile.setStatus(ChunkUploadFileStatus.UPLOADING);
                chunkUploadFile.setCreateTime(new Date());
                chunkUploadFile.setUpdateTime(new Date());
                chunkUploadFile.setAutoStore(autoStore);
                String suffix = FileVOUtils.findFileExtName(fileName);
                if (ConstantsUtils.SUFFIX_APK.equalsIgnoreCase(suffix)) {
                    chunkUploadFile.setTerminalType(TerminalTypeEnum.ANDROID.getValue());
                } else if (ConstantsUtils.SUFFIX_HAP.equalsIgnoreCase(suffix)) {
                    chunkUploadFile.setTerminalType(TerminalTypeEnum.HARMONY.getValue());
                } else if (ConstantsUtils.SUFFIX_IOS.equalsIgnoreCase(suffix)){
                    chunkUploadFile.setTerminalType(TerminalTypeEnum.IOS.getValue());
                }
                chunkUploadFileMapper.insertSelective(chunkUploadFile);
                LOG.info("第一个分片上传成功 fileName={} fileMD5={} chunkNumber{} chunkTotal={} userId={}",fileName, fileMD5, chunkNumber, chunkTotal, userId);
            }
            return chunkUploadFile;
        } finally {
            distributedLockService.unlock(key);
        }
    }

    private FileVO mergePart(FileVO saveFile, List<TChunkUploadPart> saveList) {
        File currentFile = new File(saveFile.getFilePath());
        if (currentFile.exists()) {
            // 文件已经被成功合并过,直接返回
            setFileVOSize(saveFile, currentFile.length());
            return saveFile;
        }
        List<ChunkMergeSort> sortList = saveList.stream()
                .sorted(Comparator.comparing(TChunkUploadPart::getChunkNum))
                .map(part -> new ChunkMergeSort(getPartFile(part), part.getChunkNum()))
                .collect(Collectors.toList());
        List<File> chunkFiles = sortList.stream()
                .sorted(Comparator.comparingInt(ChunkMergeSort::getNumber))
                .map(ChunkMergeSort::getChunk)
                .collect(Collectors.toList());
        if (chunkFiles.stream().anyMatch(Objects::isNull)) {
            throw new ChunkFileMergeException("合并文件失败");
        }
        byte[] buff = new byte[4096];
        int read = 0;
        // 分片合并
        BufferedOutputStream outputStream = null;
        FileInputStream inputStream = null;
        try {
            outputStream = new BufferedOutputStream(new FileOutputStream(currentFile));
            for (File chunkFile : chunkFiles) {
                inputStream = new FileInputStream(chunkFile);
                while ((read = inputStream.read(buff)) != -1) {
                    outputStream.write(buff, 0, read);
                }
                inputStream.close();
                inputStream = null;
                LOG.info("merge {}", chunkFile.getAbsolutePath());
            }
            outputStream.flush();
            outputStream.close();
            // 删除保存在fastDFS的文件
            saveList.forEach(chunkFile -> {
                if (!singleFastDfsFileService.instance().deleteFile(chunkFile.getChunkFilePath())) {
                    LOG.info("删除FastDfs上传分片文件失败 path={}", chunkFile.getChunkFilePath());
                }
            });
            chunkFiles.forEach(chunkFile -> {
                if (!chunkFile.delete()) {
                    LOG.info("删除上传分片文件失败 path={}", chunkFile.getPath());
                }
            });
            setFileVOSize(saveFile, currentFile.length());
            return saveFile;
        } catch (IOException e) {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e2) {
                    e2.getMessage();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e2) {
                    e2.getMessage();
                }
            }
            throw new ChunkFileMergeException("合并文件失败", e);
        }
    }

    private File mergePart_new(File destFile, List<String> srcPath) {
        if (destFile.exists()) {
            destFile.delete();
        }
        byte[] buff = new byte[10240];
        int read;
        // 分片合并
        FileInputStream inputStream = null;
        try(
            BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(destFile,true));
        ){

            for (String path : srcPath) {
                inputStream = new FileInputStream(path);
                while ((read = inputStream.read(buff)) != -1) {
                    outputStream.write(buff, 0, read);
                }
                inputStream.close();
            }
            outputStream.flush();
            outputStream.close();
            return destFile;
        } catch (IOException e) {
            throw new IjiamiRuntimeException("合并文件失败"+e.getMessage());
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }


    @Override
    public void chunkClean() {
        List<TChunkUploadFile> chunkUploadFileList = chunkUploadFileMapper.findByStatusAndTimeout(24,null);
        TChunkUploadFile updateFile = new TChunkUploadFile();
        TChunkUploadPart query = new TChunkUploadPart();
        for (TChunkUploadFile chunkUploadFile : chunkUploadFileList) {
            LOG.info("分片上传开始清理数据:{}", chunkUploadFile.getFileName());
            if(chunkUploadFile.getStatus()==ChunkUploadFileStatus.DELETE
                    || chunkUploadFile.getStatus()==ChunkUploadFileStatus.TIMEOUT){
                chunkUploadFileMapper.deleteByPrimaryKey(chunkUploadFile.getId());
            }else{
                updateFile.setId(chunkUploadFile.getId());
                updateFile.setStatus(ChunkUploadFileStatus.TIMEOUT);
                updateFile.setUpdateTime(new Date());
                chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
            }
            try {
                if(StringUtils.isNotBlank(chunkUploadFile.getFileLocalPath())){
                    Files.deleteIfExists(Paths.get(chunkUploadFile.getFileLocalPath()));
                }
                if (chunkUploadFile.getDfsPath()!=null && !storageLogService.existsAssetsIdNotNullByAddress(chunkUploadFile.getDfsPath())) {
                    singleFastDfsFileService.instance().deleteFile(chunkUploadFile.getDfsPath());
                }
                query.setFileId(chunkUploadFile.getId());
                List<TChunkUploadPart> saveList = chunkUploadPartMapper.select(query);
                clearChunkFilePart(saveList);
            } catch (IOException e) {
                LOG.error("分片定时清理异常",e);
            }
        }

    }

    private void setUploadChunkFileFailure(Long uploadChunkFileId) {
        TChunkUploadFile updateFile = new TChunkUploadFile();
        updateFile.setId(uploadChunkFileId);
        updateFile.setStatus(ChunkUploadFileStatus.FAILURE);
        chunkUploadFileMapper.updateByPrimaryKeySelective(updateFile);
    }

    private void clearChunkFilePart(List<TChunkUploadPart> saveList) {
        // 删除保存在fastDFS的文件
        saveList.forEach(chunkFile -> {
            if (!singleFastDfsFileService.instance().deleteFile(chunkFile.getChunkFilePath())) {
                LOG.info("删除FastDfs上传分片文件失败 path={}", chunkFile.getChunkFilePath());
            }
            File diskChunkFile = getDiskChunkFile(chunkFile.getChunkMd5());
            if (diskChunkFile.exists()) {
                diskChunkFile.delete();
//                LOG.info("删除上传分片文件失败 path={}", diskChunkFile.getPath());
            }
        });
        if(ArrayUtil.isNotEmpty(saveList) && saveList.size()>0){
            chunkUploadPartMapper.delByFileId(saveList.get(0).getFileId());
        }
    }

    private File getPartFile(TChunkUploadPart part) {
        File file = getDiskChunkFile(part.getChunkMd5());
        if (!file.exists()) {
            try {
                URL url = new URL(fastDFSIntranetIp  + "/" + part.getChunkFilePath());
                LOG.info("分片文件不存在进行下载 path={} url={} ", file.getAbsolutePath(), url);
                FileUtils.copyURLToFile(url, file);
            } catch (IOException e) {
                LOG.error("下载分片文件失败", e);
                return null;
            }
        }
        return file;
    }

    private FileVO getPartFileVO(TChunkUploadPart part, String fileName) {
        File file = getDiskChunkFile(fileName);
        if (!file.exists()) {
            try {
                URL url = new URL(fastDFSIntranetIp  + "/" + part.getChunkFilePath());
                LOG.info("分片文件不存在进行下载 path={} url={} ", file.getAbsolutePath(), url);
                FileUtils.copyURLToFile(url, file);
            } catch (IOException e) {
                LOG.error("下载分片文件失败", e);
                throw new IjiamiRuntimeException("下载分片文件失败");
            }
        }
        FileVO fileVO = new FileVO();
        String bucketName = "/default/";
        fileVO.setBucketName(bucketName);
        fileVO.setFileUrl(defaultService.getFileContext().getFilePath());
        fileVO.setFilePath(fileVO.getFileUrl() + bucketName + file.getName());
        fileVO.setFileName(file.getName());
        fileVO.setFileExtName(FileVOUtils.findFileExtName(file.getName()));
        setFileVOSize(fileVO, file.length());
        return fileVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(value = "privacy-detection:assets", allEntries = true)
    public void delete(Long id, Long userId) throws IjiamiApplicationException {
        // 更新存储记录表中的数据
        TAssets asset = assetsMapper.selectByPrimaryKey(id);
        if (Objects.isNull(asset)) {
            throw new IjiamiApplicationException("该资产不存在");
        }
        Long count = taskMapper.countWaitingOrDetectionInByMd5(asset.getCreateUserId(), asset.getMd5());
        if (count > 0) {
            throw new IjiamiApplicationException("该资产存在正在检测的任务，无法删除。");
        }
        List<TAssets> assetsList = assetsMapper.findByUserAndMd5(asset.getCreateUserId(), asset.getMd5());
        for (TAssets a:assetsList) {
            storageLogService.deleteStorage(a);
            if (StringUtils.isNotBlank(a.getDumpZipUrlPath())) {
                singleFastDfsFileService.instance().deleteFile(a.getDumpZipUrlPath());
            }
            if (userId == null) {
                assetsMapper.deleteById(a.getId());
            } else {
                assetsMapper.deleteByIdAndUserId(a.getId(), userId);
            }
            //清理关联任务
            deleteTask(a.getId());
        }
    }

    /**
     * 清理某个时间段的apk
     * @param startDate
     * @param endDate
     */
    @Override
    public void deleteApk(String startDate, String endDate) {
        List<TAssets> tAssets = assetsMapper.selectByDate(startDate, endDate);
        for (TAssets asset : tAssets) {
            Long count = taskMapper.countWaitingOrDetectionInByMd5(asset.getCreateUserId(), asset.getMd5());
            if (count > 0) {
                continue;
            }
            if (StringUtils.isNotBlank(asset.getShellIpaPath())) {
                storageLogService.deleteStorage(asset);
            }
            if (StringUtils.isNotBlank(asset.getDumpZipUrl())) {
                singleFastDfsFileService.instance().deleteFile(asset.getDumpZipUrl());
            }
            assetsMapper.logicDeleteById(asset.getId());
        }
    }

    /**
     * 批量删除任务
     * @param assetsId
     * @throws IjiamiApplicationException
     */
    @Async("commonExecutor")
    void deleteTask(Long assetsId) throws IjiamiApplicationException{
    	 List<Long>  list = taskMapper.getTaskByAssetsId(assetsId);
         if(list != null && list.size()>0) {
         	for (Long taskId : list) {
         		taskService.deleteByTaskId(taskId, null, false);
			}
         }
    }

    public boolean isSupportFileType(TerminalTypeEnum terminalTypeEnum, String extName) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return "APK".equalsIgnoreCase(extName);
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return "IPA".equalsIgnoreCase(extName);
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return "HAP".equalsIgnoreCase(extName);
        }
        return false;
    }

    /**
     * 下载HTTP文件并发送消息
     *
     * @param urlUploadVO
     * @return FileVO
     */
    public FileVO downFileSendMessage(String md5, UrlUploadVO urlUploadVO, Long userId) {
        String url = urlUploadVO.getUrl();
        FileVO fileVO = new FileVO();
        String fileUrl = commonProperties.getFilePath();
        String bucketName = "/default";
        try {
            // 初始化 url信息，校验文件类型
            HttpURLConnection urlConnection = (HttpURLConnection) new URL(CommonUtil.encodeURL(url)).openConnection();
            String fileName = checkURLContentType(url, urlConnection);
            String fileExtName = getFileExtName(fileName);
            String fileKey = bucketName + FileUtil.SEPARATOR + fileName;
            String filePath = fileUrl + fileKey;
            LOG.info("{} 下载中...", md5);
            File file = new File(filePath);
            // 文件夹是否存在
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdir();
            }
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            int responseCode = urlConnection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                // 文件下载
                urlUploadContent(md5, urlConnection, file, userId);
                // 文件FileVO数据拼接
                File file2 = new File(filePath);
                fileVO.setFileKey(FileUtil.encodeData(fileKey));
                fileVO.setFilePath(filePath);
                fileVO.setFileUrl(fileUrl);
                fileVO.setFileName(fileName);
                fileVO.setBucketName(bucketName);
                Map<String, Object> params = new HashMap<>();
                DecimalFormat df1 = new DecimalFormat("0.00");
                String parseLong = df1.format((double) file2.length() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
                params.put("size", parseLong);
                fileVO.setParams(params);
                fileVO.setFileExtName(fileExtName);
            } else {
                throw new IjiamiApplicationException("文件下载失败，请检查url是否可用");
            }
        } catch (Exception e) {
            sendAnalysisProgress(userId, e.getMessage(), e.getMessage(), null, md5, "error");
            LOG.info("{} 下载中失败", md5);
            throw new RuntimeException(e);
        }
        return fileVO;
    }

    /**
     * 文件内容下载
     *
     * @param urlConnection
     * @param outFile
     * @param userId
     * @throws IOException
     */
    private void urlUploadContent(String md5, HttpURLConnection urlConnection, File outFile, Long userId)
            throws IOException {
        InputStream inputStream = urlConnection.getInputStream();
        int len = 0;
        byte[] data = new byte[8192];
        // 用于保存当前进度（具体进度）
        int curProgress = 0;
        // 获取文件长度
        int maxProgress = urlConnection.getContentLength();
        try (FileOutputStream fos = new FileOutputStream(outFile)) {
            // 将文件大小分成100分，每一分的大小为unit
            int unit = maxProgress / 100 == 0 ? Integer.MAX_VALUE : maxProgress / 100;
            // 用于保存当前进度(1~100%)
            int unitProgress = 0;
            while (-1 != (len = inputStream.read(data))) {
                fos.write(data, 0, len);
                curProgress += len;// 保存当前具体进度
                int temp = curProgress / unit; // 计算当前百分比进度
                if (temp >= 1 && temp > unitProgress) {// 如果下载过程出现百分比变化
                    unitProgress = temp;// 保存当前百分比
                    validAnalysisStatus(md5, userId);
                    sendAnalysisProgress(userId, "文件下载", "正在下载文件...", unitProgress, md5);
                    LOG.info("{} 正在下载中...{}%", md5, unitProgress);
                }
            }
            LOG.info("{} 下载完成...", md5);
        }
    }

    private static final List<String> APP_FILE_EXT_LIST = Arrays.asList(
            ConstantsUtils.SUFFIX_END_APK,
            ConstantsUtils.SUFFIX_IPA,
            ConstantsUtils.SUFFIX_END_HAP
    );

    /**
     * url初始化、校验文件类型
     *
     * @param url
     * @return
     * @throws MalformedURLException
     * @throws IOException
     * @throws IjiamiApplicationException
     */
    private String checkURLContentType(String url, HttpURLConnection urlConnection)
            throws IOException, IjiamiApplicationException {
        urlConnection.setRequestMethod("GET");
        urlConnection.setConnectTimeout(10 * 1000);
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String currentDate = df.format(day);
        long current = System.currentTimeMillis();
        String fileNameFromUrl = CommonUtil.getFilenameFromURLIgnoringQuery(url);
        String fileName;
        if (APP_FILE_EXT_LIST.stream().anyMatch(ext -> StringUtils.endsWithIgnoreCase(fileNameFromUrl, ext))) {
            fileName = currentDate + current + fileNameFromUrl.toLowerCase();
        } else {
            if (urlConnection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                throw new IjiamiApplicationException("无效的链接");
            }
            String contentType = urlConnection.getContentType();
            String contentDisposition = urlConnection.getHeaderField("Content-Disposition");
            fileName = currentDate + current;
            if (!StringUtils.isEmpty(contentType)) {
                if (contentType.contains("application/vnd.android.package-archive")) {
                    fileName += ConstantsUtils.SUFFIX_END_APK;
                } else if (contentType.contains("application/vnd.iphone")) {
                    fileName += ConstantsUtils.SUFFIX_IPA;
                } else if (contentType.contains("application/octet-stream") && !StringUtils.isEmpty(contentDisposition)) {
                    String nameSub = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9).replaceAll("\"", "");
                    Optional<String> extOpt = APP_FILE_EXT_LIST.stream().filter(ext -> StringUtils.endsWithIgnoreCase(nameSub, ext)).findFirst();
                    if (extOpt.isPresent()) {
                        fileName += extOpt.get();
                    } else {
                        throw new IjiamiApplicationException("不支持的文件格式");
                    }
                } else {
                    throw new IjiamiApplicationException("不支持的文件格式");
                }
            } else {
                throw new IjiamiApplicationException("不支持的文件格式");
            }
        }
        return fileName;
    }

    private void validAnalysisStatus(String fileMD5, Long userId) {
        String key = PinfoConstant.CACHE_ANALYSIS_STOP + userId+fileMD5;
        String value = cacheService.get(key);
        if (STOP_FLAG.equals(value)) {
            cacheService.delete(key);
            throw new IjiamiRuntimeException("上传已撤销");
        }
    }

    private void cleanAnalysisStatus(String fileMD5, Long userId) {
        cacheService.delete(PinfoConstant.CACHE_ANALYSIS_STOP + userId+fileMD5);
    }

    /**
     * 发送分析进度消息
     */
    void sendAnalysisProgress(Long userId, String title, String content, Integer unitProgress, String md5) {
        AnalysisProgressParam param = new AnalysisProgressParam();
        param.setUserId(userId);
        param.setTitle(title);
        param.setContent(content);
        param.setUnitProgress(unitProgress);
        param.setMd5(md5);
        messageNotificationSendKit.sendAnalysisProgress(param);
    }

    /**
     * 发送分析进度消息（带类型）
     */
    void sendAnalysisProgress(Long userId, String title, String content, Integer unitProgress, String md5, String type) {
        AnalysisProgressParam param = new AnalysisProgressParam();
        param.setUserId(userId);
        param.setTitle(title);
        param.setContent(content);
        param.setUnitProgress(unitProgress);
        param.setMd5(md5);
        param.setType(type);
        messageNotificationSendKit.sendAnalysisProgress(param);
    }

    /**
     * 发送分析进度消息（包含完整的UrlMessageVO信息）
     */
    void sendAnalysisProgressWithMessageVO(Long userId, String title, String content, UrlMessageVO messageVO) {
        AnalysisProgressParam param = new AnalysisProgressParam();
        param.setUserId(userId);
        param.setTitle(title);
        param.setContent(content);
        param.setUnitProgress(messageVO.getUnitProgress());
        param.setMd5(messageVO.getMd5());
        if (messageVO.getName() != null) param.setName(messageVO.getName());
        if (messageVO.getVersion() != null) param.setVersion(messageVO.getVersion());
        if (messageVO.getPakage() != null) param.setPakage(messageVO.getPakage());
        if (messageVO.getSize() != null) param.setSize(messageVO.getSize());
        if (messageVO.getLogo() != null) param.setLogo(messageVO.getLogo());
        if (messageVO.getSignMd5() != null) param.setSignMd5(messageVO.getSignMd5());
        if (messageVO.getTerminalType() != null) param.setTerminalType(messageVO.getTerminalType().getValue());
        if (messageVO.getIsHavePacker() != null) param.setIsHavePacker(messageVO.getIsHavePacker());
        messageNotificationSendKit.sendAnalysisProgress(param);
    }

    /**
     * 解析资产文件获取应用信息
     * @param assets 资产对象
     * @return AppDetailsResult 应用详细信息结果
     */
    private AppDetailsResult analyzeAppFile(TAssets assets) {
        LOG.info("开始资产解析任务 assetsId=" + assets.getId());
        AppDetailsResult result = new AppDetailsResult();
        if (StringUtils.isNotBlank(assets.getAddress())) {
            String filePath = FileUtil.decodeData(assets.getAddress());
            File appFile = new File(filePath);
            if (!appFile.exists()) {
                String url = assets.getAppSourceUrl(fastDFSIntranetIp);
                try {
                    FileUtils.copyURLToFile(new URL(url), appFile);
                } catch (IOException e) {
                    LOG.error(String.format("下载资产失败 url=%s", url), e);
                    throw new IjiamiRuntimeException("下载资产失败");
                }
            }
            if (assets.getTerminalType() == TerminalTypeEnum.ANDROID) {
                try {
                    cn.ijiami.detection.common.utils.CommonUtil.setConfigPath(ijiamiCommonProperties.getProperty("ijiami.config.path"));
                    cn.ijiami.detection.common.utils.CommonUtil.setToolsPath(ijiamiCommonProperties.getToolsPath());
                    File decompileDir = new File(transformDecompilePath(appFile.getAbsolutePath()));
                    if (!decompileDir.exists()) {
                        decompileDir.mkdirs();
                    }
                    try {
                        if (appFile.getAbsolutePath().toLowerCase().endsWith("aab")) {
                            UnpackAAB unpackAAB = new UnpackAAB(ijiamiCommonProperties.getToolsPath());
                            unpackAAB.unpackageAAB(appFile.getAbsolutePath(), decompileDir.getAbsolutePath(), null);
                        } else {
                            UnpackAPK unpackAPK = new UnpackAPK(ijiamiCommonProperties.getToolsPath());
                            unpackAPK.unpackageAPK(appFile.getAbsolutePath(), decompileDir.getAbsolutePath(), null);
                        }
                    } catch (DecompileException e) {
                        LOG.error("解析资产失败", e);
                        throw new IjiamiRuntimeException("解析资产失败");
                    }

                    AppBasicInfo info = buildAppBasicInfo(assets);
                    info.setSignDetail(extractAppSignature(appFile.getAbsolutePath()));
                    EncryptCompanyDataModel encryption = cn.ijiami.detection.common.utils.CommonUtil.checkEncryptionCompany(decompileDir.getAbsolutePath(), CommonUtil.getFileExtName(appFile.getName()));
                    if (encryption != null) {
                        LOG.info("解析加固成功 encryption={}", encryption.getCompany());
                        info.setIjiamiEncryption(org.apache.commons.lang3.StringUtils.contains(encryption.getCompany(), "爱加密"));
                        info.setEncryptCompany(encryption.getCompany());
                    }
                    if (StringUtils.isEmpty(assets.getPermissions())
                            || Objects.isNull(assets.getTargetSdkVersion())
                            || Objects.isNull(assets.getMinSdkVersion())) {
                        try {
                            AssetParserInfo parserInfo = parserAssetInfo(filePath);
                            LOG.info("解析资产基本信息成功 usesPermissions={}", parserInfo.getUsesPermissions());
                            result.setPermissionList(getPermissionVOList(parserInfo.getUsesPermissions(), assets.getTerminalType()));
                            if (StringUtils.isNotBlank(parserInfo.getTargetSdkVersion())) {
                                info.setTargetSdkVersion(Integer.parseInt(parserInfo.getTargetSdkVersion()));
                            }
                            if (StringUtils.isNotBlank(parserInfo.getMinSdkVersion())) {
                                info.setMinSdkVersion(Integer.parseInt(parserInfo.getMinSdkVersion()));
                            }
                            updateToDb(assets.getId(), info, parserInfo.getUsesPermissions());
                        } catch (Exception e) {
                            LOG.error(String.format("解析资产基本信息失败 filePath=%s", filePath), e);
                        }
                    }
                    result.setBasicInfo(info);
                } catch (Exception e) {
                    LOG.error(String.format("解析资产失败 filePath=%s", filePath), e);
                    throw new IjiamiRuntimeException("解析资产失败");
                }
            } else if (assets.getTerminalType() == TerminalTypeEnum.IOS) {
                List<String> permissions = IpaAnalysisUtils.extractPermissionInfo(filePath);
                AppBasicInfo info = buildAppBasicInfo(assets);
                info.setIjiamiEncryption(false);
                info.setEncryptCompany(org.apache.commons.lang3.StringUtils.EMPTY);
                info.setSignDetail(org.apache.commons.lang3.StringUtils.EMPTY);
                result.setPermissionList(getPermissionVOList(permissions, assets.getTerminalType()));
                result.setBasicInfo(info);
            }
        }
        LOG.info("完成资产解析任务 assetsId=" + assets.getId());
        return result;
    }

    /**
     * 更新应用信息到数据库
     * @param id 资产ID
     * @param info 应用基本信息
     * @param detectionPermissions 检测到的权限列表
     */
    private void updateToDb(Long id, AppBasicInfo info, List<String> detectionPermissions) {
        TAssets assetsUpdate = new TAssets();
        assetsUpdate.setId(id);
        assetsUpdate.setSignature(info.getSignDetail());
        assetsUpdate.setEncryptCompany(info.getEncryptCompany());
        assetsUpdate.setTargetSdkVersion(info.getTargetSdkVersion());
        assetsUpdate.setMinSdkVersion(info.getMinSdkVersion());
        assetsUpdate.setPermissions(CommonUtil.beanToJson(detectionPermissions));
        assetsMapper.updateByPrimaryKeySelective(assetsUpdate);
    }

    @Override
    public List<TChunkUploadFile> queryAnalysisQueue(BaseQuery baseQuery) {
        List<TChunkUploadFile> tChunkUploadFiles = chunkUploadFileMapper.queryAnalysisQueue(baseQuery);
        for (TChunkUploadFile tChunkUploadFile : tChunkUploadFiles) {
            if(StringUtils.isNotEmpty(tChunkUploadFile.getAnalysisResult())){
                tChunkUploadFile.setAssets(JSONObject.parseObject(tChunkUploadFile.getAnalysisResult()));
                tChunkUploadFile.setAnalysisResult(null);
            }
        }
        return tChunkUploadFiles;
    }

    @Override
    public UploadAssetsAppendixResponse obbUpload(MultipartFile obbFile, Long userId) throws Exception {
        String extName = FilenameUtils.getExtension(obbFile.getOriginalFilename());
        File diskFile = new File(commonProperties.getProperty("ijiami.framework.file.path"), UuidUtil.uuid() + "." + extName);
        multipartFileSaveToDisk(obbFile, diskFile);
        // 文件类型是允许上传的
        if (Objects.nonNull(extName) && OBB_FILE_TYPES.contains(extName)) {
            FileVO uploadDfsFile = FileVOUtils.convertFileVOByFile(diskFile);
            FileVO fileVO = singleFastDfsFileService.instance().storeFile(uploadDfsFile);
            if (StringUtils.isBlank(fileVO.getFilePath())) {
                throw new IjiamiRuntimeException("上传失败");
            }
            LOG.info("上传obb到FastDFs filePath={}", fileVO.getFilePath());
            storageLogService.saveStorageLogByFastDfsPath(fileVO.getFilePath(), fileVO.getFilePath(), StorageFileType.OBB, userId);
            return new UploadAssetsAppendixResponse(fileVO.getFilePath());
        } else {
            LOG.info("文件类型错误 fileType={}", extName);
            throw new IjiamiRuntimeException("文件类型错误");
        }
    }

    @Transactional
    @Override
    public void saveObb(Long assetsId, String devicePath, String fileId, Long userId) {
        TStorageLog storageLog = storageLogService.findOneByAssociationKey(fileId, Collections.singletonList(StorageFileType.OBB), userId);
        if (Objects.isNull(storageLog)) {
            throw new IjiamiRuntimeException("数据不存在");
        }
        TAssets update = new TAssets();
        update.setId(assetsId);
        update.setObbDataPath(fileId);
        update.setObbDevicePath(devicePath);
        update.setUpdateTime(new Date());
        assetsMapper.updateByPrimaryKeySelective(update);
        storageLogService.updateByAssociationKey(fileId, userId);
    }

    @Transactional
    @Override
    public void batchAddApp(BatchAddApp selectApp, Long userId) throws IjiamiApplicationException, IjiamiCommandException {
        if (selectApp.getTerminalType() != TerminalTypeEnum.HARMONY.getValue()) {
            throw new IjiamiApplicationException("暂时只支持添加鸿蒙应用");
        }
        for (AddApp app:selectApp.getAppList()) {
            TAssets assets = new TAssets();
            assets.setName(app.getAppName());
            assets.setLogo("");
            assets.setVersion("");
            assets.setPakage(app.getPackageName());
            assets.setMd5(MD5Util.encode(app.getPackageName(), ""));
            assets.setSignMd5(StringUtils.EMPTY);
            assets.setUpdateUserId(userId);
            assets.setCreateUserId(userId);
            assets.setSourceFileName(StringUtils.EMPTY);
            assets.setTerminalType(TerminalTypeEnum.getItem(selectApp.getTerminalType()));
            assets.setShellIpaPath(StringUtils.EMPTY);
            assets.setPlatform(StringUtils.EMPTY);
            assets.setPrivacyPolicyPath(StringUtils.EMPTY);
            assets.setThirdPartyShareListPath(StringUtils.EMPTY);
            assets.setCreateTime(new Date());
            assets.setUpdateTime(new Date());
            assets.setIsHavePacker(PackerStatusEnum.SHELL_LESS.getValue());
            assets.setShellIpaPath(StringUtils.EMPTY);
            setAssetsFile(assets, app.getAppPrivacyPolicyFileId(), app.getThirdPartySharedListFileId(), StringUtils.EMPTY);
            saveOrUpdate(assets);
        }
    }

    @Override
    public void setAssetsFile(TAssets assets, String appPrivacyPolicyFileId, String thirdPartySharedListFileId, String qrcodeFileId) {
        if (StringUtils.isNotBlank(appPrivacyPolicyFileId)) {
            TStorageLog privacyPolicyStorageLog = storageLogService.findOneByAssociationKey(appPrivacyPolicyFileId,
                    Collections.singletonList(StorageFileType.TXT), assets.getCreateUserId());
            if (Objects.nonNull(privacyPolicyStorageLog)) {
                // 添加用户上传隐私文件的地址
                assets.setPrivacyPolicyPath(privacyPolicyStorageLog.getStorageAddress());
            } else {
                assets.setPrivacyPolicyPath(StringUtils.EMPTY);
            }
        }
        if (StringUtils.isNotBlank(thirdPartySharedListFileId)) {
            TStorageLog thirdPartyShareStorageLog = storageLogService.findOneByAssociationKey(thirdPartySharedListFileId,
                    Collections.singletonList(StorageFileType.XLSX), assets.getCreateUserId());
            if(Objects.nonNull(thirdPartyShareStorageLog)){
                // 添加用户上传的第三方共享清单地址
                assets.setThirdPartyShareListPath(thirdPartyShareStorageLog.getStorageAddress());
            } else {
                assets.setThirdPartyShareListPath(StringUtils.EMPTY);
            }
        }
        if (StringUtils.isNotBlank(qrcodeFileId)) {
            TStorageLog qrcodeStorageLog = storageLogService.findOneByAssociationKey(qrcodeFileId,
                    Collections.singletonList(StorageFileType.IMAGE), assets.getCreateUserId());
            if(Objects.nonNull(qrcodeStorageLog)){
                // 添加用户上传的二维码
                assets.setQrcodePath(qrcodeStorageLog.getStorageAddress());
            } else {
                assets.setQrcodePath(StringUtils.EMPTY);
            }
        }
    }

    /**
     * 构建应用基本信息
     * @param assets 资产对象
     * @return AppBasicInfo 应用基本信息
     */
    private AppBasicInfo buildAppBasicInfo(TAssets assets) {
        AppBasicInfo info = new AppBasicInfo();
        info.setAppName(assets.getName());
        info.setVersionName(assets.getVersion());
        info.setPackageName(assets.getPakage());
        info.setApkSize(assets.getSize());
        info.setApkMD5(assets.getMd5());
        return info;
    }

    /**
     * 从资产获取权限信息
     * @param assets 资产对象
     * @return List<PermissionVO> 权限信息列表
     */
    private List<PermissionVO> getPermissionsFromAssets(TAssets assets) {
        List<String> detectionPermissions = CommonUtil.jsonToList(assets.getPermissions(), String.class);
        return getPermissionVOList(detectionPermissions, assets.getTerminalType());
    }

    /**
     * 构建权限信息列表
     * @param detectionPermissions 检测到的权限列表
     * @param terminalType 终端类型
     * @return List<PermissionVO> 权限信息列表
     */
    private List<PermissionVO> getPermissionVOList(List<String> detectionPermissions, TerminalTypeEnum terminalType) {
        return privacyDetectionService.getPermissionVOS(new ArrayList<>(), detectionPermissions, terminalType.getValue())
                .stream()
                .filter(p -> p.getType() == PermissionSensitiveTypeEnum.PRIVACY_TYPE.value)
                .collect(Collectors.toList());
    }

    private boolean haveAppBasicInfo(TAssets assets) {
        return StringUtils.isNotBlank(assets.getPermissions());
    }

    public void startExtractAppInfoTask(TAssets assets) {
        if (Objects.isNull(assets)) {
            throw new IllegalArgumentException("资产不存在");
        }
        // 只有android和ios的资产需要额外解析
        if (assets.getTerminalType() != TerminalTypeEnum.ANDROID && assets.getTerminalType() != TerminalTypeEnum.IOS) {
            return;
        }
        if (!haveAppBasicInfo(assets)) {
            String cacheKey = CACHE_ASSETS_ANALYZE + assets.getId();
            if (cacheService.getLong(cacheKey) == null) {
                cacheService.setLong(cacheKey, assets.getId(), 30L, TimeUnit.MINUTES);
                LOG.info("启动资产解析任务 assetsId=" + assets.getId());
                executorServiceHelper.executeInAssetsAnalyzeExecutor(() -> {
                    try {
                        if (!haveAppBasicInfo(assets)) {
                            analyzeAppFile(assets);
                        } else {
                            LOG.info("资产解析任务已经完成 assetsId=" + assets.getId());
                        }
                    } catch (Throwable t) {
                        LOG.error("资产解析任务失败 assetsId=" + assets.getId(), t);
                    } finally {
                        cacheService.delete(cacheKey);
                    }
                });
            } else {
                LOG.info("资产解析任务正在执行中 assetsId=" + assets.getId());
            }
        }
    }


}
