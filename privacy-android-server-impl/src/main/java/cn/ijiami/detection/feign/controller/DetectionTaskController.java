package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.DetectionTaskServiceApi;
import cn.ijiami.detection.android.client.param.StartTaskParam;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionTaskController.java
 * @Description 任务相关接口
 * @createTime 2025年05月12日 18:15:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "任务相关接口")
@RestController
public class DetectionTaskController implements DetectionTaskServiceApi {

    @Autowired
    private ITaskService taskService;

    @PostMapping(
            value = {"/stopTask"},
            produces = {"application/json"}
    )
    public Boolean stopTask(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.stopTask(taskId);
        } catch (IjiamiApplicationException e) {
            log.error("停止任务失败", e);
            return false;
        }
    }


    @ApiOperation("启动任务接口")
    @PostMapping(
            value = {"/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public Long startTask(@Valid @RequestBody StartTaskParam param) {
        return taskService.startTask(param);
    }

}
