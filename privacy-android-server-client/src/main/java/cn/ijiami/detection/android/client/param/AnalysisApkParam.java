package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AnalysisApkParam.java
 * @Description 解析APK参数
 * @createTime 2024年09月13日 18:39:00
 */
@Data
@ApiModel(value = "AnalysisApkParam", description = "解析APK参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnalysisApkParam {

    @ApiModelProperty(value = "文件信息", required = true)
    @NotNull(message = "文件信息不能为空")
    private FileParam fileVO;

    @ApiModelProperty(value = "是否保存", required = true)
    @NotNull(message = "是否保存不能为空")
    private Boolean saveFlag;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "是否绝对路径", required = true)
    @NotNull(message = "是否绝对路径不能为空")
    private Boolean isAbsolutePath;

    @ApiModelProperty(value = "脱壳路径")
    private String shellPath;
}
