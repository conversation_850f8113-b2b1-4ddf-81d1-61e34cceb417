package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.param.StartTaskParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 *
 */
@Api("检测任务")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface DetectionTaskServiceApi {

    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/stopTask"},
            produces = {"application/json"}
    )
    Boolean stopTask(@RequestParam("taskId") Long taskId);

    @ApiOperation("启动任务接口")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/task/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    Long startTask(@Valid @RequestBody StartTaskParam param);

}
